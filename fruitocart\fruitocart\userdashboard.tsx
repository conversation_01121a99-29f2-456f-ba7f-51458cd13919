// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useEffect } from "react";
import * as echarts from "echarts";
import {
  LayoutDashboard,
  Wallet,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Users,
  Package,
  Plus,
  RefreshCw,
  LineChart,
  Bell,
  ChevronDown,
  User,
  Settings,
  HelpCircle,
  LogOut,
  Menu,
  BellRing,
} from "lucide-react";
const App: React.FC = () => {
  useEffect(() => {
    const chartDom = document.getElementById("profit-chart");
    if (chartDom) {
      const myChart = echarts.init(chartDom);
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: ["Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sun"],
        },
        yAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}%",
          },
        },
        series: [
          {
            name: "Daily Profit",
            type: "line",
            data: [1.2, 1.8, 2.5, 2.1, 2.9, 3.2, 2.7],
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "rgba(79, 70, 229, 0.6)" },
                  { offset: 1, color: "rgba(79, 70, 229, 0.1)" },
                ],
              },
            },
            lineStyle: {
              color: "#4F46E5",
            },
            itemStyle: {
              color: "#4F46E5",
            },
            smooth: true,
          },
        ],
      };
      myChart.setOption(option);
      return () => {
        myChart.dispose();
      };
    }
  }, []);
  return (
    <div className="min-h-screen bg-[#0B0F19]">
      <main className="min-h-screen">
        <div className="p-6 space-y-6">
          <h1 className="text-2xl font-bold text-[#4B8AF9]">Dashboard</h1>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Balance Card */}
            <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-400">
                  Total USDT Balance
                </h3>
                <span className="text-xs bg-[#2A3441] text-[#4B8AF9] px-2 py-1 rounded-full">
                  Main
                </span>
              </div>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-white">5,280.42</span>
                <span className="ml-1 text-sm text-gray-400">USDT</span>
              </div>
              <div className="mt-2 text-sm text-gray-500">
                ≈ ₹438,274.86 INR
              </div>
              <div className="mt-4 flex space-x-3">
                <button className="bg-[#4B8AF9] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#3B7AE9] transition-colors !rounded-button whitespace-nowrap cursor-pointer flex items-center">
                  <Plus className="w-4 h-4 mr-2" />
                  Deposit
                </button>
                <button className="bg-[#151B28] text-[#4B8AF9] border border-[#4B8AF9] px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#1A2235] transition-colors !rounded-button whitespace-nowrap cursor-pointer flex items-center">
                  <ArrowRight className="w-4 h-4 mr-2" />
                  Withdraw
                </button>
              </div>
            </div>
            {/* Total Profit Card */}
            <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-400">
                  Total Profit
                </h3>
                <span className="text-xs bg-[#1A2F25] text-[#4ADE80] px-2 py-1 rounded-full">
                  +12.5%
                </span>
              </div>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-gray-900">658.42</span>
                <span className="ml-1 text-sm text-gray-500">USDT</span>
              </div>
              <div className="mt-2 text-sm text-gray-500">≈ ₹54,648.86 INR</div>
              <div className="mt-4">
                <div className="flex items-center text-sm text-green-600">
                  <ArrowUp className="w-4 h-4 mr-1" />
                  <span>Up 2.3% from last week</span>
                </div>
              </div>
            </div>
            {/* Today's Profit Card */}
            <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-400">
                  Today's Profit
                </h3>
                <span className="text-xs bg-[#1A2F25] text-[#4ADE80] px-2 py-1 rounded-full">
                  +2.7%
                </span>
              </div>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold text-white">+142.57</span>
                <span className="ml-1 text-sm text-gray-400">USDT</span>
              </div>
              <div className="mt-2 text-sm text-gray-500">≈ ₹11,833.31 INR</div>
              <div className="mt-4">
                <div className="flex items-center text-sm text-[#4ADE80]">
                  <ArrowUp className="w-4 h-4 mr-1" />
                  <span>Up 0.5% from yesterday</span>
                </div>
              </div>
            </div>
          </div>
          {/* Profit Chart */}
          <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">Profit History</h3>
              <div className="flex space-x-2">
                <button className="bg-[#4B8AF9] text-white px-3 py-1 rounded-full text-sm font-medium !rounded-button whitespace-nowrap cursor-pointer">
                  Week
                </button>
                <button className="bg-[#1A2235] text-gray-400 px-3 py-1 rounded-full text-sm font-medium !rounded-button whitespace-nowrap cursor-pointer">
                  Month
                </button>
                <button className="bg-[#1A2235] text-gray-400 px-3 py-1 rounded-full text-sm font-medium !rounded-button whitespace-nowrap cursor-pointer">
                  Year
                </button>
              </div>
            </div>
            <div id="profit-chart" className="w-full h-64"></div>
          </div>
          {/* Quick Actions */}
          <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
            <h3 className="text-lg font-medium text-white mb-6">
              Quick Actions
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex flex-col items-center p-4 bg-[#1A2235] rounded-lg cursor-pointer hover:bg-[#202A3F] transition-colors">
                <div className="w-12 h-12 bg-[#2A3441] rounded-full flex items-center justify-center mb-3">
                  <Wallet className="w-6 h-6 text-[#4B8AF9]" />
                </div>
                <span className="text-sm font-medium text-white">Deposit</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-[#1A2235] rounded-lg cursor-pointer hover:bg-[#202A3F] transition-colors">
                <div className="w-12 h-12 bg-[#2A3441] rounded-full flex items-center justify-center mb-3">
                  <ArrowRight className="w-6 h-6 text-[#4B8AF9]" />
                </div>
                <span className="text-sm font-medium text-white">Withdraw</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-[#1A2235] rounded-lg cursor-pointer hover:bg-[#202A3F] transition-colors">
                <div className="w-12 h-12 bg-[#2A3441] rounded-full flex items-center justify-center mb-3">
                  <Users className="w-6 h-6 text-[#4B8AF9]" />
                </div>
                <span className="text-sm font-medium text-white">
                  Referrals
                </span>
              </div>
              <div className="flex flex-col items-center p-4 bg-[#1A2235] rounded-lg cursor-pointer hover:bg-[#202A3F] transition-colors">
                <div className="w-12 h-12 bg-[#2A3441] rounded-full flex items-center justify-center mb-3">
                  <Package className="w-6 h-6 text-[#4B8AF9]" />
                </div>
                <span className="text-sm font-medium text-white">Products</span>
              </div>
            </div>
          </div>
          {/* Membership Status Section */}
          <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441] mb-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">
                Membership Status
              </h3>
              <div className="flex items-center space-x-2">
                <i className="fas fa-crown text-[#FCD34D]"></i>
                <span className="text-sm font-medium text-[#FCD34D]">
                  Bronze Member
                </span>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-400">
                    Referrals Progress
                  </span>
                  <span className="text-sm text-white">24/25</span>
                </div>
                <div className="w-full bg-[#1A2235] rounded-full h-2">
                  <div
                    className="bg-[#FCD34D] h-2 rounded-full"
                    style={{ width: "96%" }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-400">
                    Deposit Progress
                  </span>
                  <span className="text-sm text-white">₹20,000/₹25,000</span>
                </div>
                <div className="w-full bg-[#1A2235] rounded-full h-2">
                  <div
                    className="bg-[#FCD34D] h-2 rounded-full"
                    style={{ width: "80%" }}
                  ></div>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4 mt-6">
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto bg-[#CD7F32] bg-opacity-20 rounded-full flex items-center justify-center mb-2">
                    <i className="fas fa-medal text-[#CD7F32]"></i>
                  </div>
                  <span className="text-xs text-gray-400">Bronze</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto bg-[#C0C0C0] bg-opacity-20 rounded-full flex items-center justify-center mb-2">
                    <i className="fas fa-medal text-[#C0C0C0]"></i>
                  </div>
                  <span className="text-xs text-gray-400">Silver</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto bg-[#FFD700] bg-opacity-20 rounded-full flex items-center justify-center mb-2">
                    <i className="fas fa-medal text-[#FFD700]"></i>
                  </div>
                  <span className="text-xs text-gray-400">Gold</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto bg-[#E5E4E2] bg-opacity-20 rounded-full flex items-center justify-center mb-2">
                    <i className="fas fa-medal text-[#E5E4E2]"></i>
                  </div>
                  <span className="text-xs text-gray-400">Platinum</span>
                </div>
              </div>
              <div className="mt-6 text-sm text-gray-400">
                <p>Next tier benefits:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Increased referral commission to 12%</li>
                  <li>Priority customer support</li>
                  <li>Lower trading fees</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Referral Section */}
          <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">
                Your Referral Link
              </h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Total Referrals: </span>
                <span className="text-sm font-medium text-white">24</span>
              </div>
            </div>
            <div className="bg-[#1A2235] p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1 mr-4">
                  <div className="text-sm text-gray-400 mb-2">
                    Share this link to earn 10% commission on referral trades
                  </div>
                  <div className="bg-[#0B0F19] p-3 rounded flex items-center justify-between">
                    <span className="text-sm text-white overflow-hidden overflow-ellipsis">
                      https://trading.com/ref/YOUR_UNIQUE_CODE
                    </span>
                    <button
                      className="ml-2 text-[#4B8AF9] hover:text-[#3B7AE9] transition-colors !rounded-button whitespace-nowrap cursor-pointer"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          "https://trading.com/ref/YOUR_UNIQUE_CODE",
                        );
                        const notification =
                          document.getElementById("copy-notification");
                        if (notification) {
                          notification.classList.remove("opacity-0");
                          setTimeout(() => {
                            notification.classList.add("opacity-0");
                          }, 2000);
                        }
                      }}
                    >
                      <i className="fas fa-copy"></i>
                    </button>
                  </div>
                </div>
                <button className="bg-[#4B8AF9] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#3B7AE9] transition-colors !rounded-button whitespace-nowrap cursor-pointer flex items-center">
                  <i className="fas fa-share-alt mr-2"></i>
                  Share
                </button>
              </div>
              <div
                id="copy-notification"
                className="mt-2 text-sm text-[#4ADE80] opacity-0 transition-opacity duration-300"
              >
                Link copied to clipboard!
              </div>
            </div>
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-[#1A2235] p-4 rounded-lg">
                <div className="text-2xl font-bold text-[#4ADE80] mb-2">
                  $1,245.80
                </div>
                <div className="text-sm text-gray-400">
                  Total Referral Earnings
                </div>
              </div>
              <div className="bg-[#1A2235] p-4 rounded-lg">
                <div className="text-2xl font-bold text-white mb-2">24</div>
                <div className="text-sm text-gray-400">Total Referrals</div>
              </div>
              <div className="bg-[#1A2235] p-4 rounded-lg">
                <div className="text-2xl font-bold text-[#4B8AF9] mb-2">
                  10%
                </div>
                <div className="text-sm text-gray-400">Commission Rate</div>
              </div>
            </div>
          </div>
          {/* Recent Transactions */}
          <div className="bg-[#151B28] rounded-lg shadow-md p-6 border border-[#2A3441]">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-white">
                Recent Transactions
              </h3>
              <button className="text-[#4B8AF9] text-sm font-medium hover:text-[#3B7AE9] transition-colors cursor-pointer">
                View All
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-[#2A3441]">
                <thead className="bg-[#1A2235]">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Type
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Amount
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-[#151B28] divide-y divide-[#2A3441]">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 bg-[#1A2F25] rounded-full flex items-center justify-center">
                          <ArrowDown className="w-4 h-4 text-[#4ADE80]" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-white">
                            Deposit
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      +1,000 USDT
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-[#1A2F25] text-[#4ADE80]">
                        Completed
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      Apr 29, 2025
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 bg-[#1A2235] rounded-full flex items-center justify-center">
                          <LineChart className="w-4 h-4 text-[#4B8AF9]" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-white">
                            Profit
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      +27.50 USDT
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-[#1A2F25] text-[#4ADE80]">
                        Completed
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      Apr 28, 2025
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 bg-[#2D1F24] rounded-full flex items-center justify-center">
                          <ArrowUp className="w-4 h-4 text-[#F87171]" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-white">
                            Withdrawal
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      -500 USDT
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-[#2D2620] text-[#FCD34D]">
                        Pending
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      Apr 27, 2025
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 bg-[#1A2235] rounded-full flex items-center justify-center">
                          <Users className="w-4 h-4 text-[#4B8AF9]" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-white">
                            Referral Bonus
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      +50 USDT
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-[#1A2F25] text-[#4ADE80]">
                        Completed
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                      Apr 26, 2025
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};
export default App;
