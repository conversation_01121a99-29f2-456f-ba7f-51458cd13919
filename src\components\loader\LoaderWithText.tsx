import React from 'react';

interface LoaderWithTextProps {
  isLoading: boolean;
  text?: string;
  spinnerColor?: string;
  textColor?: string;
  spinnerSize?: string;
  className?: string;
}

const LoaderWithText: React.FC<LoaderWithTextProps> = ({
  isLoading,
  text = "Loading...",
  spinnerColor = "border-indigo-600",
  textColor = "text-gray-600",
  spinnerSize = "w-12 h-12",
  className = ""
}) => {
  if (!isLoading) return null;

  return (
    <div className={`absolute inset-0 flex items-center justify-center ${className}`}>
      <div className="flex flex-col items-center">
        <div className={`${spinnerSize} border-4 ${spinnerColor} border-t-transparent rounded-full animate-spin`}></div>
        <p className={`mt-4 ${textColor}`}>
          {text}
        </p>
      </div>
    </div>
  );
};

export default LoaderWithText;