// GlobalContext.tsx

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  Dispatch,
  SetStateAction,
} from "react";
import { UserData } from "./models/common.model"; // Adjust the path as needed

// Step 1: Define the shape of the context
interface GlobalContextProps {
  isLoading: boolean;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  currentPage: string;
  setCurrentPage: Dispatch<SetStateAction<string>>;
  userData: UserData | undefined;
  setUserData: Dispatch<SetStateAction<UserData | undefined>>;
  isMenuExpanded: boolean;
  setIsMenuExpanded: Dispatch<SetStateAction<boolean>>;
  screenSize: string;
  setScreenSize: Dispatch<SetStateAction<string>>;
  maxDate: Date | undefined;
  setMaxDate: Dispatch<SetStateAction<Date | undefined>>;
}

// Step 2: Create the context with undefined default
const GlobalContext = createContext<GlobalContextProps | undefined>(undefined);

// Step 3: Define props for the Provider component
interface GlobalProviderProps {
  children: ReactNode;
}

// Step 4: Create the Provider
export const GlobalProvider: React.FC<GlobalProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<string>('');
  const [userData, setUserData] = useState<UserData | undefined>();
  const [isMenuExpanded, setIsMenuExpanded] = useState<boolean>(false);
  const [screenSize, setScreenSize] = useState<string>('');
  const [maxDate, setMaxDate] = useState<Date | undefined>();

  return (
    <GlobalContext.Provider
      value={{
        isLoading,
        setIsLoading,
        currentPage,
        setCurrentPage,
        userData,
        setUserData,
        isMenuExpanded,
        setIsMenuExpanded,
        screenSize,
        setScreenSize,
        maxDate,
        setMaxDate,
      }}
    >
      {children}
    </GlobalContext.Provider>
  );
};

// Step 5: Create a hook to use the context
export const useGlobalContext = (): GlobalContextProps => {
  const context = useContext(GlobalContext);
  if (!context) {
    throw new Error("useGlobalContext must be used within a GlobalProvider");
  }
  return context;
};
