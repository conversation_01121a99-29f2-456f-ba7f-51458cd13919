// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";
import * as echarts from "echarts";
import {
  LayoutDashboard,
  Wallet,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Users,
  Package,
  Plus,
  RefreshCw,
  LineChart,
  Bell,
  ChevronDown,
  User,
  Settings,
  HelpCircle,
  LogOut,
  Menu,
  BellRing,
} from "lucide-react";
const App: React.FC = () => {
  const [selectedCurrency, setSelectedCurrency] = useState("USD");
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
  const currencies = ["USD", "EUR", "GBP", "JPY", "CNY", "INR"];
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      message: "Your deposit has been confirmed",
      time: "2 hours ago",
      read: false,
    },
    {
      id: 2,
      message: "Daily profit added: +2.5%",
      time: "5 hours ago",
      read: false,
    },
    {
      id: 3,
      message: "New referral joined your network",
      time: "Yesterday",
      read: true,
    },
  ]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const unreadNotificationsCount = notifications.filter((n) => !n.read).length;
  const markAllAsRead = () => {
    setNotifications(notifications.map((n) => ({ ...n, read: true })));
  };
  return (
    <div className="min-h-screen bg-[#0F1115] flex flex-col">
      {/* Header */}
      <header className="bg-[#1A1D24] shadow-sm border-b border-[#2A2F3A] h-16 flex items-center justify-between px-4 lg:px-8 fixed top-0 left-0 right-0 z-10">
        {/* Logo */}
        <div className="flex items-center">
          <div className="text-[#4C7BF4] font-bold text-xl flex items-center">
            <LineChart className="w-6 h-6 mr-2" />
            <span>TradePro</span>
          </div>
        </div>
        {/* Currency Selector */}
        <div className="hidden md:flex items-center ml-auto mr-8">
          <div className="relative">
            <button
              onClick={() => setShowCurrencyDropdown(!showCurrencyDropdown)}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg border border-[#2A2F3A] hover:border-[#4C7BF4] transition-colors cursor-pointer"
            >
              <span className="text-sm font-medium text-white">
                {selectedCurrency}
              </span>
              <ChevronDown className="w-4 h-4 text-[#6B7280]" />
            </button>
            {showCurrencyDropdown && (
              <div className="absolute top-full mt-1 w-24 bg-[#1A1D24] rounded-lg shadow-lg py-1 z-20 border border-[#2A2F3A]">
                {currencies.map((currency) => (
                  <button
                    key={currency}
                    onClick={() => {
                      setSelectedCurrency(currency);
                      setShowCurrencyDropdown(false);
                    }}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-[#2A2F3A] ${selectedCurrency === currency ? "text-[#4C7BF4] bg-[#2A2F3A]" : "text-white"}`}
                  >
                    {currency}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
        {/* User Actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 text-gray-600 hover:text-indigo-600 transition-colors cursor-pointer"
            >
              <Bell className="w-6 h-6" />
              {unreadNotificationsCount > 0 && (
                <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {unreadNotificationsCount}
                </span>
              )}
            </button>
            {/* Notifications Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-20 border border-gray-200">
                <div className="px-4 py-2 border-b border-gray-100 flex justify-between items-center">
                  <h3 className="text-sm font-medium text-gray-900">
                    Notifications
                  </h3>
                  <button
                    onClick={markAllAsRead}
                    className="text-xs text-indigo-600 hover:text-indigo-800 cursor-pointer"
                  >
                    Mark all as read
                  </button>
                </div>
                <div className="max-h-80 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`px-4 py-3 hover:bg-gray-50 ${!notification.read ? "bg-indigo-50" : ""}`}
                    >
                      <div className="flex items-start">
                        <div
                          className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${!notification.read ? "bg-indigo-100" : "bg-gray-100"}`}
                        >
                          <BellRing
                            className={`w-4 h-4 ${!notification.read ? "text-indigo-600" : "text-gray-500"}`}
                          />
                        </div>
                        <div className="ml-3 flex-1">
                          <p
                            className={`text-sm ${!notification.read ? "font-medium text-gray-900" : "text-gray-600"}`}
                          >
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {notification.time}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="px-4 py-2 border-t border-gray-100 text-center">
                  <button className="text-sm text-indigo-600 hover:text-indigo-800 cursor-pointer">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>
          {/* Profile */}
          <div className="relative">
            <button
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <div className="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center text-white text-sm font-medium">
                JD
              </div>
              <span className="hidden md:block text-sm font-medium text-gray-700">
                John Doe
              </span>
              <ChevronDown className="w-4 h-4 text-gray-500" />
            </button>
            {/* Profile Dropdown */}
            {showProfileDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-20 border border-gray-200">
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700"
                >
                  <User className="w-4 h-4 inline mr-2 text-gray-400" /> Profile
                </a>
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700"
                >
                  <Settings className="w-4 h-4 inline mr-2 text-gray-400" />{" "}
                  Settings
                </a>
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700"
                >
                  <HelpCircle className="w-4 h-4 inline mr-2 text-gray-400" />{" "}
                  Help Center
                </a>
                <div className="border-t border-gray-100 my-1"></div>
                <a
                  href="#"
                  className="block px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4 inline mr-2" /> Sign out
                </a>
              </div>
            )}
          </div>
        </div>
      </header>
      {/* Main Content */}
      <div className="flex flex-1 pt-16">
        {/* Sidebar */}

        {/* Mobile Sidebar Toggle */}
        <div className="fixed bottom-4 right-4 md:hidden z-20">
          <button className="bg-indigo-600 text-white h-12 w-12 rounded-full shadow-lg flex items-center justify-center cursor-pointer">
            <Menu className="w-6 h-6" />
          </button>
        </div>
        {/* Main Content Area */}
        <main className="flex-1 ml-0 md:ml-64 min-h-screen"></main>
      </div>
    </div>
  );
};
export default App;
