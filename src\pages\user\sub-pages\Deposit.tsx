import { Copy } from "lucide-react";
import React, { useState } from "react";
const Deposit: React.FC = () => {
    const [notifications, setNotifications] = useState([
        {
            id: 1,
            message: "Your deposit has been confirmed",
            time: "2 hours ago",
            read: false,
        },
        {
            id: 2,
            message: "Daily profit added: +2.5%",
            time: "5 hours ago",
            read: false,
        },
        {
            id: 3,
            message: "New referral joined your network",
            time: "Yesterday",
            read: true,
        },
    ]);
    const [selectedAmount, setSelectedAmount] = useState("");
    const [transactionId, setTransactionId] = useState("");
    const [copySuccess, setCopySuccess] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [sortBy, setSortBy] = useState("date");
    const [sortDirection, setSortDirection] = useState("desc");
    const adminWalletAddress = "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F";
    const depositOptions = [
        { value: "50", label: "50 USDT" },
        { value: "100", label: "100 USDT" },
        { value: "250", label: "250 USDT" },
        { value: "500", label: "500 USDT" },
        { value: "1000", label: "1000 USDT" },
    ];
    const depositHistory = [
        {
            id: 1,
            date: "Apr 29, 2025 14:32",
            amount: 100,
            txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
            status: "Approved",
        },
        {
            id: 2,
            date: "Apr 27, 2025 09:15",
            amount: 250,
            txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
            status: "Approved",
        },
        {
            id: 3,
            date: "Apr 25, 2025 18:45",
            amount: 500,
            txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
            status: "Pending",
        },
        {
            id: 4,
            date: "Apr 20, 2025 11:20",
            amount: 50,
            txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
            status: "Rejected",
        },
        {
            id: 5,
            date: "Apr 15, 2025 16:05",
            amount: 100,
            txId: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
            status: "Approved",
        },
    ];
    const markAllAsRead = () => {
        setNotifications(notifications.map((n) => ({ ...n, read: true })));
    };
    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
    };
    const handleSubmitDeposit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle deposit submission logic here
        alert(
            `Deposit request submitted: ${selectedAmount} USDT with Transaction ID: ${transactionId}`,
        );
        setSelectedAmount("");
        setTransactionId("");
    };
    const filteredHistory = depositHistory
        .filter(
            (item) =>
                item.txId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.date.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.amount.toString().includes(searchTerm),
        )
        .sort((a, b) => {
            if (sortBy === "date") {
                return sortDirection === "asc"
                    ? new Date(a.date).getTime() - new Date(b.date).getTime()
                    : new Date(b.date).getTime() - new Date(a.date).getTime();
            } else if (sortBy === "amount") {
                return sortDirection === "asc"
                    ? a.amount - b.amount
                    : b.amount - a.amount;
            }
            return 0;
        });
    const itemsPerPage = 5;
    const totalPages = Math.ceil(filteredHistory.length / itemsPerPage);
    const paginatedHistory = filteredHistory.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage,
    );
    const handleSort = (column: string) => {
        if (sortBy === column) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc");
        } else {
            setSortBy(column);
            setSortDirection("asc");
        }
    };
    const totalDeposited = depositHistory
        .filter((item) => item.status === "Approved")
        .reduce((sum, item) => sum + item.amount, 0);
    const currentBalance = 5280.42; // This would come from your actual data
    const usdtRate = 1.0; // This would be fetched from an API
  
    return (
        <div className="min-h-screen p-6 gap-6">
            <div className="flex items-center mb-6">
                <h1 className="text-2xl font-bold text-gray-900">Deposit USDT</h1>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Admin Wallet Section */}
                <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6 border border-gray-100">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">
                        Admin USDT Wallet (TRC20)
                    </h2>
                    <div className="flex flex-col md:flex-row gap-6">
                        <div className="flex-1">
                            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 mb-4 relative">
                                <p className="text-sm font-medium text-gray-800 break-all">
                                    {adminWalletAddress}
                                </p>
                                <button
                                    onClick={() => copyToClipboard(adminWalletAddress)}
                                    className="absolute right-2 top-3 text-indigo-600 hover:text-indigo-800 cursor-pointer"
                                >
                                    <Copy className="h-5 w-5" />
                                </button>
                                {copySuccess && (
                                    <div className="absolute -top-10 right-0 bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                                        Copied to clipboard!
                                    </div>
                                )}
                            </div>
                            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm text-yellow-700">
                                            <strong>Important:</strong> Only send USDT via the TRC20
                                            network. Other networks are not supported and may result
                                            in loss of funds.
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div className="text-sm text-gray-600 mb-4">
                                <p className="mb-2">
                                    <strong>Current Exchange Rate:</strong> 1 USDT = $
                                    {usdtRate.toFixed(2)} USD
                                </p>
                                <p>
                                    After sending USDT, please submit your transaction ID in the
                                    form below to complete your deposit request.
                                </p>
                            </div>
                        </div>
                        <div className="w-full md:w-48 flex flex-col items-center">
                            <div className="bg-white p-2 border border-gray-200 rounded-lg mb-2">
                                <div className="bg-gray-100 p-4 rounded">
                                    <img
                                        src="https://readdy.ai/api/search-image?query=QR%20code%20for%20cryptocurrency%20wallet%20address%2C%20clean%20minimalist%20design%2C%20high%20contrast%20black%20pattern%20on%20white%20background%2C%20clear%20and%20scannable%2C%20professional%20fintech%20style%2C%20digital%20payment%20technology&width=200&height=200&seq=1&orientation=squarish"
                                        alt="USDT Wallet QR Code"
                                        className="w-full h-auto"
                                    />
                                </div>
                            </div>
                            <p className="text-xs text-center text-gray-500">
                                Scan to deposit USDT
                            </p>
                        </div>
                    </div>
                </div>
                {/* Summary Section */}
                <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Summary</h2>
                    <div className="space-y-4">
                        <div>
                            <p className="text-sm text-gray-500 mb-1">
                                Total Deposited Amount
                            </p>
                            <div className="flex items-baseline">
                                <span className="text-2xl font-bold text-gray-900">
                                    {totalDeposited}
                                </span>
                                <span className="ml-1 text-sm text-gray-500">USDT</span>
                            </div>
                            <p className="text-xs text-gray-500">
                                (≈ ${(totalDeposited * usdtRate).toFixed(2)} USD)
                            </p>
                        </div>
                        <div className="border-t border-gray-100 pt-4">
                            <p className="text-sm text-gray-500 mb-1">Current Balance</p>
                            <div className="flex items-baseline">
                                <span className="text-2xl font-bold text-gray-900">
                                    {currentBalance.toFixed(2)}
                                </span>
                                <span className="ml-1 text-sm text-gray-500">USDT</span>
                            </div>
                            <p className="text-xs text-gray-500">
                                (≈ ${(currentBalance * usdtRate).toFixed(2)} USD)
                            </p>
                            <div className="flex items-center mt-1 text-green-600 text-xs">
                                <i className="fas fa-arrow-up mr-1"></i>
                                <span>+2.7% from yesterday</span>
                            </div>
                        </div>
                        <div className="border-t border-gray-100 pt-4 text-xs text-gray-500">
                            <p>Last updated: Apr 29, 2025, 14:30</p>
                        </div>
                    </div>
                </div>
                {/* Deposit Request Form */}
                <div className="col-span-3 bg-white rounded-lg shadow-md p-6 border border-gray-100">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">
                        Deposit Request Form
                    </h2>
                    <form onSubmit={handleSubmitDeposit} className="max-w-2xl">
                        <div className="mb-4">
                            <label
                                htmlFor="amount"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Select Amount
                            </label>
                            <select
                                id="amount"
                                value={selectedAmount}
                                onChange={(e) => setSelectedAmount(e.target.value)}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                                required
                            >
                                <option value="" disabled>
                                    Select an amount
                                </option>
                                {depositOptions.map((option) => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="mb-4">
                            <label
                                htmlFor="transactionId"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >
                                Enter Transaction ID
                            </label>
                            <input
                                type="text"
                                id="transactionId"
                                value={transactionId}
                                onChange={(e) => setTransactionId(e.target.value)}
                                placeholder="Enter the transaction ID from your wallet"
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                                required
                            />
                            <p className="mt-1 text-xs text-gray-500">
                                You can find the transaction ID in your wallet after sending USDT
                                to the admin wallet address.
                            </p>
                        </div>
                        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <i className="fas fa-exclamation-triangle text-yellow-400"></i>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-yellow-700">
                                        Please ensure you enter the correct transaction ID. Incorrect
                                        IDs may delay the processing of your deposit.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <button
                            type="submit"
                            className="bg-indigo-600 text-white px-6 py-3 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors !rounded-button whitespace-nowrap cursor-pointer"
                        >
                            Submit Deposit Request
                        </button>
                    </form>
                </div>
                {/* Deposit Logs Table */}
                <div className="col-span-3 bg-white rounded-lg shadow-md p-6 border border-gray-100">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">
                        Deposit History
                    </h2>
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search deposits..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                            />
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i className="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th
                                        scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        onClick={() => handleSort("date")}
                                    >
                                        <div className="flex items-center">
                                            Date & Time
                                            {sortBy === "date" && (
                                                <i
                                                    className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                                                ></i>
                                            )}
                                        </div>
                                    </th>
                                    <th
                                        scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                        onClick={() => handleSort("amount")}
                                    >
                                        <div className="flex items-center">
                                            Amount (USDT)
                                            {sortBy === "amount" && (
                                                <i
                                                    className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                                                ></i>
                                            )}
                                        </div>
                                    </th>
                                    <th
                                        scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                    >
                                        Transaction ID
                                    </th>
                                    <th
                                        scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                    >
                                        Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {paginatedHistory.length > 0 ? (
                                    paginatedHistory.map((item) => (
                                        <tr key={item.id}>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {item.date}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {item.amount} USDT
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <div className="flex items-center">
                                                    <span className="truncate max-w-xs">
                                                        {item.txId.substring(0, 10)}...
                                                    </span>
                                                    <button
                                                        onClick={() => copyToClipboard(item.txId)}
                                                        className="ml-2 text-indigo-600 hover:text-indigo-800 cursor-pointer"
                                                    >
                                                        <i className="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span
                                                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.status === "Approved"
                                                            ? "bg-green-100 text-green-800"
                                                            : item.status === "Pending"
                                                                ? "bg-yellow-100 text-yellow-800"
                                                                : "bg-red-100 text-red-800"
                                                        }`}
                                                >
                                                    {item.status}
                                                </span>
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td
                                            colSpan={4}
                                            className="px-6 py-4 text-center text-sm text-gray-500"
                                        >
                                            No deposit history found
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                    {totalPages > 1 && (
                        <div className="flex items-center justify-between mt-4">
                            <div className="text-sm text-gray-700">
                                Showing{" "}
                                <span className="font-medium">
                                    {(currentPage - 1) * itemsPerPage + 1}
                                </span>{" "}
                                to{" "}
                                <span className="font-medium">
                                    {Math.min(currentPage * itemsPerPage, filteredHistory.length)}
                                </span>{" "}
                                of <span className="font-medium">{filteredHistory.length}</span>{" "}
                                results
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                                    disabled={currentPage === 1}
                                    className={`px-3 py-1 rounded-md ${currentPage === 1 ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-indigo-600 hover:bg-indigo-50 cursor-pointer"} border border-gray-300`}
                                >
                                    Previous
                                </button>
                                <button
                                    onClick={() =>
                                        setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                                    }
                                    disabled={currentPage === totalPages}
                                    className={`px-3 py-1 rounded-md ${currentPage === totalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-indigo-600 hover:bg-indigo-50 cursor-pointer"} border border-gray-300`}
                                >
                                    Next
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

        </div>
    );
};
export default Deposit;
