
import React, { useState, useEffect } from "react";
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Search, 
  ArrowUp, 
  ArrowDown, 
  X, 
  CheckCircle,
  Clock,
  XCircle,
  DollarSign,
  Wallet
} from "lucide-react";

const UserWithdraw: React.FC = () => {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      message: "Your withdrawal has been processed",
      time: "2 hours ago",
      read: false,
    },
    {
      id: 2,
      message: "Daily profit added: +2.5%",
      time: "5 hours ago",
      read: false,
    },
    {
      id: 3,
      message: "New referral joined your network",
      time: "Yesterday",
      read: true,
    },
  ]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [withdrawalAmount, setWithdrawalAmount] = useState("");
  const [walletAddress, setWalletAddress] = useState("");
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [copySuccess, setCopySuccess] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [twoFactorCode, setTwoFactorCode] = useState("");
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const unreadNotificationsCount = notifications.filter((n) => !n.read).length;
  
  const withdrawalOptions = [
    { value: "50", label: "50 USDT" },
    { value: "100", label: "100 USDT" },
    { value: "250", label: "250 USDT" },
    { value: "500", label: "500 USDT" },
    { value: "1000", label: "1000 USDT" },
  ];
  
  const networkOptions = [
    { value: "trc20", label: "TRC20 (TRON)", fee: 1 },
    { value: "erc20", label: "ERC20 (Ethereum)", fee: 15 },
    { value: "bep20", label: "BEP20 (Binance Smart Chain)", fee: 5 },
  ];
  
  const withdrawalHistory = [
    {
      id: 1,
      date: "Apr 29, 2025 14:32",
      amount: 100,
      walletAddress: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      network: "TRC20",
      status: "Completed",
    },
    {
      id: 2,
      date: "Apr 27, 2025 09:15",
      amount: 250,
      walletAddress: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      network: "TRC20",
      status: "Completed",
    },
    {
      id: 3,
      date: "Apr 25, 2025 18:45",
      amount: 500,
      walletAddress: "******************************************",
      network: "ERC20",
      status: "Pending",
    },
    {
      id: 4,
      date: "Apr 20, 2025 11:20",
      amount: 50,
      walletAddress: "bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m",
      network: "BEP20",
      status: "Failed",
    },
    {
      id: 5,
      date: "Apr 15, 2025 16:05",
      amount: 100,
      walletAddress: "TXo2Uvz3zQkL4CvcS6AWuFWxqz9uCHBV9F",
      network: "TRC20",
      status: "Completed",
    },
  ];

  // Constants
  const currentBalance = 5280.42;
  const dailyWithdrawalLimit = 2000;
  const minWithdrawal = 25;
  const maxWithdrawal = 10000;
  const usdtRate = 1.0;

  // Calculated values
  const pendingWithdrawals = withdrawalHistory
    .filter((item) => item.status === "Pending")
    .reduce((sum, item) => sum + item.amount, 0);
  
  const totalWithdrawn = withdrawalHistory
    .filter((item) => item.status === "Completed")
    .reduce((sum, item) => sum + item.amount, 0);
    
  const remainingDailyLimit = dailyWithdrawalLimit - pendingWithdrawals;
  const availableBalance = currentBalance - pendingWithdrawals;
  const selectedNetworkFee = networkOptions.find(n => n.value === selectedNetwork)?.fee || 0;
  const totalDeduction = Number(withdrawalAmount) + selectedNetworkFee;

  // Real-time validation
  useEffect(() => {
    const newErrors: {[key: string]: string} = {};
    
    if (withdrawalAmount) {
      const amount = Number(withdrawalAmount);
      
      if (amount < minWithdrawal) {
        newErrors.amount = `Minimum withdrawal amount is ${minWithdrawal} USDT`;
      } else if (amount > maxWithdrawal) {
        newErrors.amount = `Maximum withdrawal amount is ${maxWithdrawal} USDT`;
      } else if (amount > availableBalance) {
        newErrors.amount = "Insufficient balance";
      } else if (amount > remainingDailyLimit) {
        newErrors.amount = `Exceeds daily withdrawal limit. Remaining: ${remainingDailyLimit} USDT`;
      } else if (selectedNetwork && totalDeduction > availableBalance) {
        newErrors.amount = `Insufficient balance including network fee (${selectedNetworkFee} USDT)`;
      }
    }

    if (walletAddress) {
      // Basic wallet address validation
      if (selectedNetwork === "trc20" && !walletAddress.startsWith("T")) {
        newErrors.wallet = "Invalid TRON wallet address. Must start with 'T'";
      } else if (selectedNetwork === "erc20" && !walletAddress.startsWith("0x")) {
        newErrors.wallet = "Invalid Ethereum wallet address. Must start with '0x'";
      } else if (selectedNetwork === "bep20" && !walletAddress.match(/^(0x|bnb)/)) {
        newErrors.wallet = "Invalid BSC wallet address. Must start with '0x' or 'bnb'";
      } else if (walletAddress.length < 20) {
        newErrors.wallet = "Wallet address seems too short";
      }
    }

    setErrors(newErrors);
  }, [withdrawalAmount, walletAddress, selectedNetwork, availableBalance, remainingDailyLimit]);

  const markAllAsRead = () => {
    setNotifications(notifications.map((n) => ({ ...n, read: true })));
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!withdrawalAmount) {
      newErrors.amount = "Withdrawal amount is required";
    }
    if (!walletAddress) {
      newErrors.wallet = "Wallet address is required";
    }
    if (!selectedNetwork) {
      newErrors.network = "Please select a network";
    }
    
    setErrors(prev => ({...prev, ...newErrors}));
    return Object.keys(newErrors).length === 0 && Object.keys(errors).length === 0;
  };

  const handleSubmitWithdrawal = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      setShowConfirmation(true);
    }
  };

  const confirmWithdrawal = async () => {
    if (!twoFactorCode) {
      setErrors(prev => ({...prev, twoFactor: "2FA code is required"}));
      return;
    }
    
    if (twoFactorCode.length !== 6) {
      setErrors(prev => ({...prev, twoFactor: "2FA code must be 6 digits"}));
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert(
        `Withdrawal request submitted successfully!\n\nDetails:\n- Amount: ${withdrawalAmount} USDT\n- Network Fee: ${selectedNetworkFee} USDT\n- Total Deducted: ${totalDeduction} USDT\n- Wallet: ${walletAddress}\n- Network: ${selectedNetwork.toUpperCase()}\n\nProcessing time: 1-24 hours`
      );
      
      // Reset form
      setWithdrawalAmount("");
      setWalletAddress("");
      setSelectedNetwork("");
      setTwoFactorCode("");
      setShowConfirmation(false);
      setErrors({});
    } catch (error) {
      setErrors(prev => ({...prev, submit: "Failed to submit withdrawal request. Please try again."}));
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredHistory = withdrawalHistory
    .filter(
      (item) =>
        item.walletAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.date.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.amount.toString().includes(searchTerm) ||
        item.network.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortBy === "date") {
        return sortDirection === "asc"
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortBy === "amount") {
        return sortDirection === "asc"
          ? a.amount - b.amount
          : b.amount - a.amount;
      }
      return 0;
    });

  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredHistory.length / itemsPerPage);
  const paginatedHistory = filteredHistory.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case "Pending":
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case "Failed":
        return <XCircle className="w-4 h-4 text-red-400" />;
      default:
        return null;
    }
  };

  const isFormValid = !Object.keys(errors).length && withdrawalAmount && walletAddress && selectedNetwork;

  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-1 min-h-screen">
        <div className="p-6 space-y-6">
          <div className="flex items-center mb-6">
            <Wallet className="w-8 h-8 text-blue-400 mr-3" />
            <h1 className="text-3xl font-bold text-white">Withdraw USDT</h1>
          </div>

          {/* Success Message */}
          {copySuccess && (
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-400 mr-2" />
                <p className="text-green-400 text-sm">Address copied to clipboard!</p>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Withdrawal Form */}
            <div className="lg:col-span-2 bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-700">
              <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                <DollarSign className="w-6 h-6 mr-2 text-blue-400" />
                Withdrawal Request Form
              </h2>
              
              <form onSubmit={handleSubmitWithdrawal} className="space-y-6">
                {/* Amount Input */}
                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-300 mb-2">
                    Withdrawal Amount
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      id="amount"
                      value={withdrawalAmount}
                      onChange={(e) => setWithdrawalAmount(e.target.value)}
                      min={minWithdrawal}
                      max={maxWithdrawal}
                      step="0.01"
                      className={`w-full px-4 py-3 bg-gray-700 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 transition-colors ${
                        errors.amount ? 'border-red-500' : 'border-gray-600'
                      }`}
                      placeholder={`Enter amount (min. ${minWithdrawal} USDT)`}
                    />
                    <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 font-medium">
                      USDT
                    </span>
                  </div>
                  
                  {errors.amount && (
                    <p className="mt-2 text-sm text-red-400 flex items-center">
                      <AlertTriangle className="w-4 h-4 mr-1" />
                      {errors.amount}
                    </p>
                  )}
                  
                  {Number(withdrawalAmount) >= minWithdrawal && !errors.amount && (
                    <div className="mt-2 space-y-1">
                      <p className="text-sm text-green-400">
                        ≈ ₹{(Number(withdrawalAmount) * 83.27).toFixed(2)} INR
                      </p>
                      {selectedNetwork && (
                        <p className="text-sm text-yellow-400">
                          Network fee: {selectedNetworkFee} USDT | Total deduction: {totalDeduction} USDT
                        </p>
                      )}
                    </div>
                  )}
                  
                  <div className="mt-3 flex flex-wrap gap-2">
                    {withdrawalOptions.map((option) => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => setWithdrawalAmount(option.value)}
                        className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 text-sm rounded-md transition-colors border border-gray-600"
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                  
                  <p className="mt-2 text-xs text-gray-500">
                    Daily limit: {dailyWithdrawalLimit} USDT | Remaining: {remainingDailyLimit} USDT
                  </p>
                </div>

                {/* Wallet Address */}
                <div>
                  <label htmlFor="walletAddress" className="block text-sm font-medium text-gray-300 mb-2">
                    Wallet Address
                  </label>
                  <input
                    type="text"
                    id="walletAddress"
                    value={walletAddress}
                    onChange={(e) => setWalletAddress(e.target.value.trim())}
                    placeholder="Enter your wallet address"
                    className={`w-full px-4 py-3 bg-gray-700 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 transition-colors ${
                      errors.wallet ? 'border-red-500' : 'border-gray-600'
                    }`}
                  />
                  {errors.wallet && (
                    <p className="mt-2 text-sm text-red-400 flex items-center">
                      <AlertTriangle className="w-4 h-4 mr-1" />
                      {errors.wallet}
                    </p>
                  )}
                  <p className="mt-2 text-xs text-gray-500">
                    Double-check your wallet address. Incorrect addresses cannot be recovered.
                  </p>
                </div>

                {/* Network Selection */}
                <div>
                  <label htmlFor="network" className="block text-sm font-medium text-gray-300 mb-2">
                    Select Network
                  </label>
                  <select
                    id="network"
                    value={selectedNetwork}
                    onChange={(e) => setSelectedNetwork(e.target.value)}
                    className={`w-full px-4 py-3 bg-gray-700 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white transition-colors ${
                      errors.network ? 'border-red-500' : 'border-gray-600'
                    }`}
                  >
                    <option value="" disabled className="text-gray-400">
                      Select a network
                    </option>
                    {networkOptions.map((option) => (
                      <option key={option.value} value={option.value} className="bg-gray-700 text-white">
                        {option.label} - Fee: {option.fee} USDT
                      </option>
                    ))}
                  </select>
                  {errors.network && (
                    <p className="mt-2 text-sm text-red-400 flex items-center">
                      <AlertTriangle className="w-4 h-4 mr-1" />
                      {errors.network}
                    </p>
                  )}
                </div>

                {/* Warning */}
                <div className="bg-yellow-900/20 border-l-4 border-yellow-500 p-4 rounded-r-lg">
                  <div className="flex">
                    <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-yellow-200">
                        <strong>Important:</strong> Ensure that your wallet supports the selected network. 
                        Withdrawals to incorrect networks may result in permanent loss of funds.
                      </p>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={!isFormValid}
                  className={`w-full py-3 rounded-lg text-sm font-medium transition-colors ${
                    isFormValid
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  Request Withdrawal
                </button>
              </form>
            </div>

            {/* Summary Section */}
            <div className="bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-700">
              <h2 className="text-xl font-semibold text-white mb-6">
                Account Summary
              </h2>
              <div className="space-y-6">
                <div className="bg-gray-700/50 p-4 rounded-lg border border-gray-600">
                  <p className="text-sm text-gray-400 mb-1">Current Balance</p>
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold text-white">
                      {currentBalance.toFixed(2)}
                    </span>
                    <span className="ml-1 text-sm text-gray-400">USDT</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    ≈ ${(currentBalance * usdtRate).toFixed(2)} USD
                  </p>
                </div>

                <div className="bg-gray-700/50 p-4 rounded-lg border border-gray-600">
                  <p className="text-sm text-gray-400 mb-1">Available for Withdrawal</p>
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold text-green-400">
                      {availableBalance.toFixed(2)}
                    </span>
                    <span className="ml-1 text-sm text-gray-400">USDT</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    ≈ ${(availableBalance * usdtRate).toFixed(2)} USD
                  </p>
                </div>

                <div className="bg-gray-700/50 p-4 rounded-lg border border-gray-600">
                  <p className="text-sm text-gray-400 mb-1">Pending Withdrawals</p>
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold text-yellow-400">
                      {pendingWithdrawals.toFixed(2)}
                    </span>
                    <span className="ml-1 text-sm text-gray-400">USDT</span>
                  </div>
                </div>

                <div className="bg-gray-700/50 p-4 rounded-lg border border-gray-600">
                  <p className="text-sm text-gray-400 mb-1">Total Withdrawn</p>
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold text-blue-400">
                      {totalWithdrawn.toFixed(2)}
                    </span>
                    <span className="ml-1 text-sm text-gray-400">USDT</span>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-700 text-xs text-gray-500">
                  <p>Last updated: May 23, 2025, 14:30</p>
                </div>
              </div>
            </div>
          </div>

          {/* Withdrawal History Table */}
          <div className="bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-6">
              Withdrawal History
            </h2>
            
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search withdrawals..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 text-sm w-64"
                />
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-700/50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white transition-colors"
                      onClick={() => handleSort("date")}
                    >
                      <div className="flex items-center">
                        Date & Time
                        {sortBy === "date" && (
                          sortDirection === "asc" ? 
                            <ArrowUp className="w-4 h-4 ml-1" /> : 
                            <ArrowDown className="w-4 h-4 ml-1" />
                        )}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white transition-colors"
                      onClick={() => handleSort("amount")}
                    >
                      <div className="flex items-center">
                        Amount (USDT)
                        {sortBy === "amount" && (
                          sortDirection === "asc" ? 
                            <ArrowUp className="w-4 h-4 ml-1" /> : 
                            <ArrowDown className="w-4 h-4 ml-1" />
                        )}
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Wallet Address
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Network
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-700">
                  {paginatedHistory.length > 0 ? (
                    paginatedHistory.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-700/50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                          {item.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white font-medium">
                          {item.amount} USDT
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                          <div className="flex items-center">
                            <span className="truncate max-w-xs font-mono">
                              {item.walletAddress.substring(0, 10)}...{item.walletAddress.slice(-6)}
                            </span>
                            <button
                              onClick={() => copyToClipboard(item.walletAddress)}
                              className="ml-2 text-blue-400 hover:text-blue-300 transition-colors"
                            >
                              <Copy className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300 font-medium">
                          {item.network}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {getStatusIcon(item.status)}
                            <span
                              className={`ml-2 px-3 py-1 text-xs font-semibold rounded-full ${
                                item.status === "Completed"
                                  ? "bg-green-900/40 text-green-400 border border-green-500/30"
                                  : item.status === "Pending"
                                  ? "bg-yellow-900/40 text-yellow-400 border border-yellow-500/30"
                                  : "bg-red-900/40 text-red-400 border border-red-500/30"
                              }`}
                            >
                              {item.status}
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-8 text-center text-sm text-gray-400">
                        No withdrawal history found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-400">
                  Showing{" "}
                  <span className="font-medium text-white">
                    {(currentPage - 1) * itemsPerPage + 1}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium text-white">
                    {Math.min(currentPage * itemsPerPage, filteredHistory.length)}
                  </span>{" "}
                  of{" "}
                  <span className="font-medium text-white">{filteredHistory.length}</span>{" "}
                  results
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === 1
                        ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                        : "bg-gray-700 text-blue-400 hover:bg-gray-600 cursor-pointer"
                    } border border-gray-600 transition-colors`}
                  >
                    Previous
                  </button>
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === totalPages
                        ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                        : "bg-gray-700 text-blue-400 hover:bg-gray-600 cursor-pointer"
                    } border border-gray-600 transition-colors`}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-2xl p-6 max-w-md w-full border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">
                Confirm Withdrawal
              </h3>
              <button
                onClick={() => setShowConfirmation(false)}
                className="text-gray-400 hover:text-gray-300 cursor-pointer transition-colors"
              >
                <i className="lucide-x"></i>
              </button>
            </div>
            <div className="mb-6">
              <p className="text-sm text-gray-300 mb-4">
                Please review your withdrawal details:
              </p>
              <div className="bg-gray-700 p-4 rounded-lg mb-4 border border-gray-600">
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-400">Amount:</span>
                  <span className="text-sm font-medium text-white">
                    {withdrawalAmount} USDT
                  </span>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-400">Wallet Address:</span>
                  <span className="text-sm font-medium truncate max-w-[200px] text-white">
                    {walletAddress}
                  </span>
                </div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-400">Network:</span>
                  <span className="text-sm font-medium text-white">{selectedNetwork}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Fee:</span>
                  <span className="text-sm font-medium text-white">
                    {selectedNetwork === "trc20"
                      ? "1"
                      : selectedNetwork === "erc20"
                      ? "15"
                      : "5"}{" "}
                    USDT
                  </span>
                </div>
              </div>
              <div className="mb-4">
                <label
                  htmlFor="twoFactorCode"
                  className="block text-sm font-medium text-gray-300 mb-1"
                >
                  Enter 2FA Code
                </label>
                <input
                  type="text"
                  id="twoFactorCode"
                  value={twoFactorCode}
                  onChange={(e) => setTwoFactorCode(e.target.value)}
                  placeholder="Enter your 2FA code"
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400"
                  required
                />
              </div>
              <div className="bg-yellow-900/20 border-l-4 border-yellow-500 p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <i className="lucide-alert-triangle text-yellow-500"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-xs text-yellow-200">
                      Withdrawals cannot be reversed. Please ensure all details
                      are correct.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="px-4 py-2 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:bg-gray-700 cursor-pointer transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmWithdrawal}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 cursor-pointer transition-colors"
              >
                Confirm Withdrawal
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default UserWithdraw;
