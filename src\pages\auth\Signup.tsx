// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useRef, useEffect } from "react";
import {
  Eye,
  EyeOff,
  Upload,
  X,
  FileText,
  Loader,
  HelpCircle,
} from "lucide-react";
const Signup: React.FC = () => {
  // Form state
  const [formData, setFormData] = useState({
    fullName: "",
    dateOfBirth: "",
    nationality: "",
    countryOfResidence: "",
    currency: "",
    address: "",
    phoneNumber: "",
    email: "",
    password: "",
    referralCode: "",
  });
  // Dropdown visibility states
  const [showNationalityDropdown, setShowNationalityDropdown] = useState(false);
  const [showResidenceDropdown, setShowResidenceDropdown] = useState(false);
  const [showCurrencyDropdown, setShowCurrencyDropdown] = useState(false);
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!(event.target as HTMLElement).closest(".relative")) {
        setShowNationalityDropdown(false);
        setShowResidenceDropdown(false);
        setShowCurrencyDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  // Password visibility state
  const [showPassword, setShowPassword] = useState(false);
  // Document upload states
  const [aadhaarFile, setAadhaarFile] = useState<File | null>(null);
  const [passportFile, setPassportFile] = useState<File | null>(null);
  const [aadhaarPreview, setAadhaarPreview] = useState<string | null>(null);
  const [passportPreview, setPassportPreview] = useState<string | null>(null);
  // Checkbox states
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Email OTP states
  const [isOTPSent, setIsOTPSent] = useState(false);
  const [isOTPSending, setIsOTPSending] = useState(false);
  const [isOTPVerifying, setIsOTPVerifying] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [otpTimer, setOtpTimer] = useState(60);
  const [otpError, setOtpError] = useState("");
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  // Password strength
  const [passwordStrength, setPasswordStrength] = useState(0);
  // File input refs
  const aadhaarInputRef = useRef<HTMLInputElement>(null);
  const passportInputRef = useRef<HTMLInputElement>(null);
  // Country and currency data
  const countries = [
    { code: "US", name: "United States" },
    { code: "GB", name: "United Kingdom" },
    { code: "CA", name: "Canada" },
    { code: "AU", name: "Australia" },
    { code: "IN", name: "India" },
    // Add more countries as needed
  ];
  const currencies = [
    { code: "USD", name: "US Dollar" },
    { code: "EUR", name: "Euro" },
    { code: "GBP", name: "British Pound" },
    { code: "JPY", name: "Japanese Yen" },
    { code: "AUD", name: "Australian Dollar" },
    // Add more currencies as needed
  ];
  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
    // Calculate password strength
    if (name === "password") {
      let strength = 0;
      if (value.length >= 8) strength += 1;
      if (/[A-Z]/.test(value)) strength += 1;
      if (/[0-9]/.test(value)) strength += 1;
      if (/[^A-Za-z0-9]/.test(value)) strength += 1;
      setPasswordStrength(strength);
    }
  };
  // Handle file uploads
  const handleFileUpload = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "aadhaar" | "passport",
  ) => {
    const file = e.target.files?.[0] || null;
    if (!file) return;
    if (type === "aadhaar") {
      setAadhaarFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setAadhaarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setPassportFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setPassportPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  // Trigger file input click
  const triggerFileInput = (type: "aadhaar" | "passport") => {
    if (type === "aadhaar" && aadhaarInputRef.current) {
      aadhaarInputRef.current.click();
    } else if (type === "passport" && passportInputRef.current) {
      passportInputRef.current.click();
    }
  };
  // Handle OTP sending
  const handleSendOTP = () => {
    setIsOTPSending(true);
    setOtpError("");
    // Simulate API call to send OTP
    setTimeout(() => {
      setIsOTPSending(false);
      setIsOTPSent(true);
      // Start countdown timer
      let countdown = 60;
      const timer = setInterval(() => {
        countdown -= 1;
        setOtpTimer(countdown);
        if (countdown === 0) {
          clearInterval(timer);
          setIsOTPSent(false);
          setOtpTimer(60);
        }
      }, 1000);
    }, 1500);
  };
  // Handle OTP verification
  const handleVerifyOTP = () => {
    setIsOTPVerifying(true);
    setOtpError("");
    // Simulate API call to verify OTP
    setTimeout(() => {
      setIsOTPVerifying(false);
      if (otpValue === "123456") {
        // This is just for demo, replace with actual verification
        setIsEmailVerified(true);
        setOtpError("");
      } else {
        setOtpError("Invalid OTP. Please try again.");
      }
    }, 1500);
  };
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEmailVerified) {
      setOtpError("Please verify your email address first.");
      return;
    }
    setIsSubmitting(true);
    // Simulate API call
    setTimeout(() => {
      console.log("Form submitted:", formData);
      console.log("Aadhaar file:", aadhaarFile);
      console.log("Passport file:", passportFile);
      setIsSubmitting(false);
      // Here you would typically redirect to a success page or login
    }, 2000);
  };
  // Calculate form completion percentage
  const calculateProgress = () => {
    let filledFields = 0;
    let totalRequiredFields = 10; // Including new required fields
    if (formData.fullName) filledFields++;
    if (formData.dateOfBirth) filledFields++;
    if (formData.nationality) filledFields++;
    if (formData.countryOfResidence) filledFields++;
    if (formData.currency) filledFields++;
    if (formData.address) filledFields++;
    if (formData.phoneNumber) filledFields++;
    if (formData.email) filledFields++;
    if (formData.password) filledFields++;
    if (aadhaarFile) filledFields++;
    if (passportFile) filledFields++;
    if (termsAccepted) filledFields++;
    if (privacyAccepted) filledFields++;
    return Math.round((filledFields / (totalRequiredFields + 3)) * 100);
  };
  return (
    <div className="min-h-screen bg-[#0D1117] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto bg-[#1A1F2E] rounded-lg shadow-lg overflow-hidden">
        {/* Progress bar */}
        <div className="w-full bg-gray-200 h-2">
          <div
            className="bg-blue-600 h-2 transition-all duration-500 ease-in-out"
            style={{ width: `${calculateProgress()}%` }}
          ></div>
        </div>
        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-10">
            <h1 className="text-3xl font-extrabold text-white mb-2">Sign Up</h1>
            <p className="text-gray-400 max-w-md mx-auto">
              Complete the form below to create your account. All fields marked
              with * are required.
            </p>
          </div>
          <form onSubmit={handleSubmit}>
            {/* Personal Information Section */}
            <div className="mb-10">
              <h2 className="text-xl font-bold text-white mb-6 pb-2 border-b border-gray-700">
                Personal Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Full Name */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="fullName"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                    required
                  />
                </div>
                {/* Date of Birth */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="dateOfBirth"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Date of Birth *
                  </label>
                  <input
                    type="date"
                    id="dateOfBirth"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white date-input"
                    required
                  />
                </div>
                {/* Nationality */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="nationality"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Nationality *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="nationality"
                      name="nationality"
                      value={formData.nationality}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          nationality: e.target.value,
                        });
                        setShowNationalityDropdown(true);
                      }}
                      onFocus={() => setShowNationalityDropdown(true)}
                      placeholder="Search nationality"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    {showNationalityDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {countries
                          .filter((country) =>
                            country.name
                              .toLowerCase()
                              .includes(formData.nationality.toLowerCase()),
                          )
                          .map((country) => (
                            <div
                              key={country.code}
                              className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                              onClick={() => {
                                setFormData({
                                  ...formData,
                                  nationality: country.name,
                                });
                                setShowNationalityDropdown(false);
                              }}
                            >
                              {country.name}
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {/* Country of Residence */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="countryOfResidence"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Country of Residence *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="countryOfResidence"
                      name="countryOfResidence"
                      value={formData.countryOfResidence}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          countryOfResidence: e.target.value,
                        });
                        setShowResidenceDropdown(true);
                      }}
                      onFocus={() => setShowResidenceDropdown(true)}
                      placeholder="Search country of residence"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    {showResidenceDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {countries
                          .filter((country) =>
                            country.name
                              .toLowerCase()
                              .includes(
                                formData.countryOfResidence.toLowerCase(),
                              ),
                          )
                          .map((country) => (
                            <div
                              key={country.code}
                              className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                              onClick={() => {
                                setFormData({
                                  ...formData,
                                  countryOfResidence: country.name,
                                });
                                setShowResidenceDropdown(false);
                              }}
                            >
                              {country.name}
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {/* Currency */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="currency"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Preferred Currency *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="currency"
                      name="currency"
                      value={formData.currency}
                      onChange={(e) => {
                        setFormData({ ...formData, currency: e.target.value });
                        setShowCurrencyDropdown(true);
                      }}
                      onFocus={() => setShowCurrencyDropdown(true)}
                      placeholder="Search currency"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    {showCurrencyDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                        {currencies
                          .filter(
                            (currency) =>
                              currency.name
                                .toLowerCase()
                                .includes(formData.currency.toLowerCase()) ||
                              currency.code
                                .toLowerCase()
                                .includes(formData.currency.toLowerCase()),
                          )
                          .map((currency) => (
                            <div
                              key={currency.code}
                              className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                              onClick={() => {
                                setFormData({
                                  ...formData,
                                  currency: `${currency.name} (${currency.code})`,
                                });
                                setShowCurrencyDropdown(false);
                              }}
                            >
                              {currency.name} ({currency.code})
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
                {/* Address */}
                <div className="col-span-2">
                  <label
                    htmlFor="address"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Address *
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="Enter your full address"
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                    required
                  />
                </div>
                {/* Phone Number */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="phoneNumber"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Phone Number *
                  </label>
                  <div className="flex">
                    <select className="px-3 py-2 border border-r-0 border-gray-700 bg-[#1A1F2E] rounded-l-md text-white text-sm">
                      <option>+1</option>
                      <option>+44</option>
                      <option>+91</option>
                      <option>+61</option>
                    </select>
                    <input
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      placeholder="Enter your phone number"
                      className="w-full px-4 py-2 border border-gray-700 bg-[#1A1F2E] rounded-r-md focus:ring-blue-500 focus:border-blue-500 text-white text-sm"
                      required
                    />
                  </div>
                </div>
                {/* Email */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Email Address *
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email address"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => handleSendOTP()}
                      disabled={!formData.email || isOTPSent}
                      className={`px-4 py-2 text-sm font-medium rounded-md text-white ${
                        isOTPSent
                          ? "bg-gray-400 cursor-not-allowed"
                          : "bg-blue-600 hover:bg-blue-700"
                      } transition-colors !rounded-button whitespace-nowrap`}
                    >
                      {isOTPSending ? (
                        <Loader className="w-4 h-4 animate-spin" />
                      ) : isOTPSent ? (
                        `Resend in ${otpTimer}s`
                      ) : (
                        "Send OTP"
                      )}
                    </button>
                  </div>
                </div>
                {/* OTP Verification */}
                {isOTPSent && (
                  <div className="col-span-2 sm:col-span-1">
                    <label
                      htmlFor="otp"
                      className="block text-sm font-medium text-gray-300 mb-1"
                    >
                      Enter OTP *
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        id="otp"
                        maxLength={6}
                        value={otpValue}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, "");
                          setOtpValue(value);
                        }}
                        placeholder="Enter 6-digit OTP"
                        className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => handleVerifyOTP()}
                        disabled={otpValue.length !== 6 || isOTPVerifying}
                        className={`px-4 py-2 text-sm font-medium rounded-md text-white ${
                          otpValue.length !== 6
                            ? "bg-gray-400 cursor-not-allowed"
                            : "bg-blue-600 hover:bg-blue-700"
                        } transition-colors !rounded-button whitespace-nowrap`}
                      >
                        {isOTPVerifying ? (
                          <Loader className="w-4 h-4 animate-spin" />
                        ) : (
                          "Verify OTP"
                        )}
                      </button>
                    </div>
                    {otpError && (
                      <p className="mt-1 text-sm text-red-600">{otpError}</p>
                    )}
                  </div>
                )}
                {/* Password */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      placeholder="Create a strong password"
                      className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                      required
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="w-5 h-5 text-gray-400" />
                      ) : (
                        <Eye className="w-5 h-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  {/* Password strength indicator */}
                  <div className="mt-2">
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            passwordStrength === 0
                              ? "bg-gray-200"
                              : passwordStrength === 1
                                ? "bg-red-500"
                                : passwordStrength === 2
                                  ? "bg-yellow-500"
                                  : passwordStrength === 3
                                    ? "bg-blue-500"
                                    : "bg-green-500"
                          }`}
                          style={{ width: `${passwordStrength * 25}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">
                        {passwordStrength === 0
                          ? "Weak"
                          : passwordStrength === 1
                            ? "Fair"
                            : passwordStrength === 2
                              ? "Good"
                              : passwordStrength === 3
                                ? "Strong"
                                : "Very Strong"}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Use 8+ characters with a mix of letters, numbers & symbols
                    </p>
                  </div>
                </div>
                {/* Referral Code (Optional) */}
                <div className="col-span-2 sm:col-span-1">
                  <label
                    htmlFor="referralCode"
                    className="block text-sm font-medium text-gray-300 mb-1"
                  >
                    Referral Code{" "}
                    <span className="text-xs">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="referralCode"
                    name="referralCode"
                    value={formData.referralCode}
                    onChange={handleInputChange}
                    placeholder="Enter referral code if you have one"
                    className="w-full px-4 py-2 border border-gray-700 rounded-md focus:ring-[#3B82F6] focus:border-[#3B82F6] text-sm bg-[#1A1F2E] text-white"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter a referral code to get special benefits
                  </p>
                </div>
              </div>
            </div>
            {/* Document Upload Section */}
            <div className="mb-10">
              <h2 className="text-xl font-bold text-white mb-6 pb-2 border-b border-gray-700">
                Document Upload
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Aadhaar Document Upload */}
                <div className="col-span-2 sm:col-span-1">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Aadhaar Document *
                  </label>
                  <input
                    type="file"
                    ref={aadhaarInputRef}
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, "aadhaar")}
                  />
                  {!aadhaarPreview ? (
                    <div
                      onClick={() => triggerFileInput("aadhaar")}
                      className="border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-[#242938] transition-colors"
                    >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-1">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-400">
                        PDF, JPG, JPEG or PNG (Max 5MB)
                      </p>
                    </div>
                  ) : (
                    <div className="relative border rounded-lg overflow-hidden">
                      <div className="absolute top-2 right-2 z-10">
                        <button
                          type="button"
                          onClick={() => {
                            setAadhaarFile(null);
                            setAadhaarPreview(null);
                          }}
                          className="bg-red-100 text-red-600 p-1 rounded-full hover:bg-red-200 transition-colors !rounded-button whitespace-nowrap"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                      {aadhaarFile?.type.includes("image") ? (
                        <img
                          src={aadhaarPreview}
                          alt="Aadhaar Preview"
                          className="w-full h-40 object-cover object-top"
                        />
                      ) : (
                        <div className="w-full h-40 bg-gray-100 flex items-center justify-center">
                          <div className="text-center">
                            <FileText className="w-8 h-8 text-red-500 mb-2" />
                            <p className="text-sm text-gray-700">
                              {aadhaarFile?.name}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                {/* Passport Document Upload */}
                <div className="col-span-2 sm:col-span-1">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Passport Document *
                  </label>
                  <input
                    type="file"
                    ref={passportInputRef}
                    className="hidden"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, "passport")}
                  />
                  {!passportPreview ? (
                    <div
                      onClick={() => triggerFileInput("passport")}
                      className="border-2 border-dashed border-gray-700 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-[#242938] transition-colors"
                    >
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-1">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-400">
                        PDF, JPG, JPEG or PNG (Max 5MB)
                      </p>
                    </div>
                  ) : (
                    <div className="relative border rounded-lg overflow-hidden">
                      <div className="absolute top-2 right-2 z-10">
                        <button
                          type="button"
                          onClick={() => {
                            setPassportFile(null);
                            setPassportPreview(null);
                          }}
                          className="bg-red-100 text-red-600 p-1 rounded-full hover:bg-red-200 transition-colors !rounded-button whitespace-nowrap"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                      {passportFile?.type.includes("image") ? (
                        <img
                          src={passportPreview}
                          alt="Passport Preview"
                          className="w-full h-40 object-cover object-top"
                        />
                      ) : (
                        <div className="w-full h-40 bg-gray-100 flex items-center justify-center">
                          <div className="text-center">
                            <FileText className="w-8 h-8 text-red-500 mb-2" />
                            <p className="text-sm text-gray-700">
                              {passportFile?.name}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            {/* Terms and Submission */}
            <div className="mb-8">
              <div className="space-y-4">
                {/* Terms Checkbox */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="terms"
                      name="terms"
                      type="checkbox"
                      checked={termsAccepted}
                      onChange={() => setTermsAccepted(!termsAccepted)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label
                      htmlFor="terms"
                      className="font-medium text-gray-500"
                    >
                      I agree to the{" "}
                      <a href="#" className="text-blue-600 hover:underline">
                        Terms and Conditions
                      </a>{" "}
                      *
                    </label>
                  </div>
                </div>
                {/* Privacy Policy Checkbox */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="privacy"
                      name="privacy"
                      type="checkbox"
                      checked={privacyAccepted}
                      onChange={() => setPrivacyAccepted(!privacyAccepted)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label
                      htmlFor="privacy"
                      className="font-medium text-gray-500"
                    >
                      I agree to the{" "}
                      <a href="#" className="text-blue-600 hover:underline">
                        Privacy Policy
                      </a>{" "}
                      *
                    </label>
                  </div>
                </div>
              </div>
            </div>
            {/* Submit Button */}
            <div className="flex flex-col items-center">
              <button
                type="submit"
                disabled={isSubmitting || !termsAccepted || !privacyAccepted}
                className={`w-full sm:w-auto px-8 py-3 text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${
                  isSubmitting || !termsAccepted || !privacyAccepted
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                } !rounded-button whitespace-nowrap cursor-pointer`}
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <Loader className="w-5 h-5 mr-2 animate-spin" /> Creating
                    Account...
                  </span>
                ) : (
                  "Create Account"
                )}
              </button>
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  Already have an account?{" "}
                  <a
                    href="/"
                    className="text-blue-600 hover:underline font-medium"
                  >
                    Sign in
                  </a>
                </p>
              </div>
            </div>
          </form>
        </div>
        {/* Help Button */}
        <div className="fixed bottom-6 right-6">
          <button
            type="button"
            className="bg-gradient-to-r from-[#4A6FFF] to-[#6C8FFF] text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 !rounded-button whitespace-nowrap cursor-pointer"
          >
            <HelpCircle className="w-6 h-6" />
          </button>
        </div>
      </div>
    </div>
  );
};
export default Signup;
