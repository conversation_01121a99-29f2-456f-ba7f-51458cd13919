// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { JSX, useState } from "react";
import * as echarts from "echarts";
import {
    LayoutDashboard,
    Users,
    Wallet,
    ArrowDownToLine,
    TrendingUp,
    DollarSign,
    Layers,
    Package,
    Bell,
    User,
    ChevronDown,
    ChevronLeft,
    ChevronRight,
    Search,
    Filter,
    Eye,
    Edit,
    Key,
    UserCheck,
    Trash2,
    CloudUpload,
    ToggleLeft,
    ToggleRight,
    Flag,
    HandCoins,
    LineChart,
    ArrowUp,
    ArrowDown,
    Banknote,
    CheckCircle,
    UserPlus,
    LucideIcon,
    MoreVertical,
    Check,
    X,
    Clock,
    XCircle,
} from "lucide-react";
import AdminSideBar from "../../components/sidebar/AdminSideBar";
const AdminDashboard: React.FC = () => {
    const [activeTab, setActiveTab] = useState("dashboard");
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const iconsMap: Record<string, JSX.Element> = {
        dashboard: <LayoutDashboard className="w-5 h-5" />,
        users: <Users className="w-5 h-5" />,
        deposits: <Wallet className="w-5 h-5" />,
        withdrawals: <HandCoins className="w-5 h-5" />,
        profit: <LineChart className="w-5 h-5" />,
        currency: <DollarSign className="w-5 h-5" />,
        "deposit-slabs": <Layers className="w-5 h-5" />,
        products: <Package className="w-5 h-5" />,
    };
    const iconMap: Record<string, LucideIcon> = {
        "fa-user-plus": UserPlus,
        "fa-money-bill-wave": Banknote,
        "fa-hand-holding-usd": HandCoins,
        "fa-check-circle": CheckCircle,
        "fa-chart-line": LineChart,
    };
    const [enabled, setEnabled] = useState(false);
    // Initialize charts after component mounts
    React.useEffect(() => {
        if (activeTab === "dashboard") {
            // User growth chart
            const userChart = echarts.init(
                document.getElementById("userGrowthChart"),
            );
            userChart.setOption({
                animation: false,
                tooltip: {
                    trigger: "axis",
                    axisPointer: {
                        type: "shadow",
                    },
                },
                grid: {
                    left: "3%",
                    right: "4%",
                    bottom: "3%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                    axisTick: {
                        alignWithLabel: true,
                    },
                },
                yAxis: {
                    type: "value",
                },
                series: [
                    {
                        name: "New Users",
                        type: "bar",
                        barWidth: "60%",
                        data: [120, 200, 150, 180, 230, 270],
                        itemStyle: {
                            color: "#4F46E5",
                        },
                    },
                ],
            });
            // Revenue chart
            const revenueChart = echarts.init(
                document.getElementById("revenueChart"),
            );
            revenueChart.setOption({
                animation: false,
                tooltip: {
                    trigger: "axis",
                },
                grid: {
                    left: "3%",
                    right: "4%",
                    bottom: "3%",
                    containLabel: true,
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                },
                yAxis: {
                    type: "value",
                },
                series: [
                    {
                        name: "Revenue",
                        type: "line",
                        smooth: true,
                        data: [8500, 11000, 12500, 14000, 15500, 17000],
                        areaStyle: {
                            color: {
                                type: "linear",
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: "rgba(79, 70, 229, 0.6)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(79, 70, 229, 0.1)",
                                    },
                                ],
                            },
                        },
                        lineStyle: {
                            color: "#4F46E5",
                            width: 3,
                        },
                        itemStyle: {
                            color: "#4F46E5",
                        },
                    },
                ],
            });
            // Handle resize
            const handleResize = () => {
                userChart.resize();
                revenueChart.resize();
            };
            window.addEventListener("resize", handleResize);
            return () => {
                userChart.dispose();
                revenueChart.dispose();
                window.removeEventListener("resize", handleResize);
            };
        }
    }, [activeTab]);
    // Add loading state
    const [isLoading, setIsLoading] = useState(true);
    const handleTabChange = (tabId: string) => {
        if (tabId === "dashboard") {
            setIsLoading(true);
            setActiveTab(tabId);
            // Simulate loading for charts initialization
            setTimeout(() => {
                setIsLoading(false);
            }, 800);
        } else {
            setActiveTab(tabId);
        }
    };
    const toggleSidebar = () => {
        setSidebarOpen(!sidebarOpen);
    };
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    return (
        <div className="flex h-screen">
            {/* Delete Confirmation Dialog */}
            <div
                id="deleteConfirmDialog"
                className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
            >
                <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white">
                    <div className="mt-3 text-center">
                        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 text-red-600">
                            <i className="fas fa-exclamation-triangle text-xl"></i>
                        </div>
                        <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4">
                            Delete User
                        </h3>
                        <div className="mt-2 px-7 py-3">
                            <p className="text-sm text-gray-500">
                                Are you sure you want to delete this user? This action cannot be
                                undone and will permanently delete the user account and all
                                associated data.
                            </p>
                        </div>
                        <div className="flex justify-center gap-4 mt-4">
                            <button
                                onClick={() => {
                                    const dialog = document.getElementById("deleteConfirmDialog");
                                    if (dialog) dialog.classList.add("hidden");
                                }}
                                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 text-sm font-medium rounded-lg !rounded-button whitespace-nowrap"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={() => {
                                    // Handle delete action here
                                    const dialog = document.getElementById("deleteConfirmDialog");
                                    if (dialog) dialog.classList.add("hidden");
                                }}
                                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg !rounded-button whitespace-nowrap"
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <AdminSideBar />
            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="bg-white shadow-sm z-10">
                    <div className="flex items-center justify-between h-16 px-6">
                        <div className="flex items-center">
                            <h1 className="text-xl font-semibold text-gray-800">
                                {activeTab === "dashboard" && "Dashboard"}
                                {activeTab === "users" && "User Management"}
                                {activeTab === "deposits" && "Deposit Verification"}
                                {activeTab === "withdrawals" && "Withdrawal Requests"}
                                {activeTab === "profit" && "Profit Management"}
                                {activeTab === "currency" && "Currency Settings"}
                                {activeTab === "deposit-slabs" && "Deposit Slabs"}
                                {activeTab === "products" && "Products Management"}
                            </h1>
                        </div>
                        <div className="flex items-center space-x-4">
                            <button className="relative p-1 text-gray-500 hover:text-gray-700 cursor-pointer !rounded-button whitespace-nowrap">
                                <Bell className="w-6 h-6" />
                                <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
                            </button>
                            <div className="relative">
                                <button className="flex items-center space-x-2 cursor-pointer !rounded-button whitespace-nowrap">
                                    <div className="w-8 h-8 rounded-full bg-indigo-600 flex items-center justify-center text-white">
                                        <User className="w-5 h-5" />
                                    </div>
                                    <span className="text-gray-700 font-medium">Admin User</span>
                                    <ChevronDown className="w-4 h-4 text-gray-500" />
                                </button>
                            </div>
                        </div>
                    </div>
                </header>
                {/* Main Content Area */}
                <main className="flex-1 overflow-y-auto p-6">
                    {/* Dashboard */}
                    {activeTab === "dashboard" && (
                        <div
                            className={`transition-opacity duration-300 ${isLoading ? "opacity-0" : "opacity-100"}`}
                        >
                            {isLoading && (
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="flex flex-col items-center">
                                        <div className="w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
                                        <p className="mt-4 text-gray-600">
                                            Loading dashboard data...
                                        </p>
                                    </div>
                                </div>
                            )}
                            {/* Stats Cards */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                                <div className="bg-white rounded-lg shadow p-5">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm">Total Users</p>
                                            <h3 className="text-2xl font-bold text-gray-800">
                                                8,249
                                            </h3>
                                            <p className="text-green-500 text-sm mt-1 flex items-center">
                                                <ArrowUp className="mr-1 h-4 w-4" />
                                                <span>12.5% from last month</span>
                                            </p>
                                        </div>
                                        <div className="bg-indigo-100 p-3 rounded-full">
                                            <Users className="text-indigo-600 w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-lg shadow p-5">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm">Total Deposits</p>
                                            <h3 className="text-2xl font-bold text-gray-800">
                                                USDT 1.2M
                                            </h3>
                                            <p className="text-green-500 text-sm mt-1 flex items-center">
                                                <ArrowUp className="mr-1 h-4 w-4" />
                                                <span>8.2% from last month</span>
                                            </p>
                                        </div>
                                        <div className="bg-green-100 p-3 rounded-full">
                                            <Banknote className="text-green-600 w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-lg shadow p-5">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm">Daily Profit</p>
                                            <h3 className="text-2xl font-bold text-gray-800">2.4%</h3>
                                            <p className="text-red-500 text-sm mt-1 flex items-center">
                                                <ArrowDown className="mr-1 h-4 w-4" />
                                                <span>0.5% from yesterday</span>
                                            </p>
                                        </div>
                                        <div className="bg-blue-100 p-3 rounded-full">
                                            <LineChart className="text-blue-600 w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-lg shadow p-5">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm">Total Earnings</p>
                                            <h3 className="text-2xl font-bold text-gray-800">
                                                USDT 89,241
                                            </h3>
                                            <p className="text-green-500 text-sm mt-1 flex items-center">
                                                <ArrowUp className="mr-1 h-4 w-4" />
                                                <span>15.3% from last month</span>
                                            </p>
                                        </div>
                                        <div className="bg-purple-100 p-3 rounded-full">
                                            <DollarSign className="text-purple-600 w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/* Charts */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                                <div className="bg-white rounded-lg shadow p-5">
                                    <div className="flex justify-between items-center mb-4">
                                        <h3 className="text-lg font-semibold text-gray-800">
                                            User Growth
                                        </h3>
                                        <div className="flex space-x-2">
                                            <button className="px-3 py-1 text-xs bg-indigo-100 text-indigo-700 rounded-full cursor-pointer !rounded-button whitespace-nowrap">
                                                Monthly
                                            </button>
                                            <button className="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full cursor-pointer !rounded-button whitespace-nowrap">
                                                Yearly
                                            </button>
                                        </div>
                                    </div>
                                    <div id="userGrowthChart" className="h-64 w-full"></div>
                                </div>
                                <div className="bg-white rounded-lg shadow p-5">
                                    <div className="flex justify-between items-center mb-4">
                                        <h3 className="text-lg font-semibold text-gray-800">
                                            Revenue Overview
                                        </h3>
                                        <div className="flex space-x-2">
                                            <button className="px-3 py-1 text-xs bg-indigo-100 text-indigo-700 rounded-full cursor-pointer !rounded-button whitespace-nowrap">
                                                Monthly
                                            </button>
                                            <button className="px-3 py-1 text-xs text-gray-500 hover:bg-gray-100 rounded-full cursor-pointer !rounded-button whitespace-nowrap">
                                                Yearly
                                            </button>
                                        </div>
                                    </div>
                                    <div id="revenueChart" className="h-64 w-full"></div>
                                </div>
                            </div>
                            {/* Recent Activity */}
                            <div className="bg-white rounded-lg shadow overflow-hidden">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Recent Activity
                                    </h3>
                                </div>
                                <div className="divide-y divide-gray-200">
                                    {[
                                        {
                                            icon: "fa-user-plus",
                                            iconBg: "bg-green-100",
                                            iconColor: "text-green-600",
                                            title: "New user registered",
                                            description: "John Smith completed registration",
                                            time: "2 minutes ago",
                                        },
                                        {
                                            icon: "fa-money-bill-wave",
                                            iconBg: "bg-blue-100",
                                            iconColor: "text-blue-600",
                                            title: "Deposit request",
                                            description: "Emma Johnson deposited $5,000",
                                            time: "15 minutes ago",
                                        },
                                        {
                                            icon: "fa-hand-holding-usd",
                                            iconBg: "bg-yellow-100",
                                            iconColor: "text-yellow-600",
                                            title: "Withdrawal request",
                                            description: "Michael Brown requested $2,500 withdrawal",
                                            time: "1 hour ago",
                                        },
                                        {
                                            icon: "fa-check-circle",
                                            iconBg: "bg-purple-100",
                                            iconColor: "text-purple-600",
                                            title: "KYC verified",
                                            description: "Sarah Davis completed KYC verification",
                                            time: "3 hours ago",
                                        },
                                        {
                                            icon: "fa-chart-line",
                                            iconBg: "bg-indigo-100",
                                            iconColor: "text-indigo-600",
                                            title: "Profit rate updated",
                                            description: "Daily profit rate updated to 2.4%",
                                            time: "5 hours ago",
                                        },
                                    ].map((item, index) => {
                                        const LucideIcon = iconMap[item.icon];

                                        return (
                                            <div key={index} className="px-6 py-4">
                                                <div className="flex items-start">
                                                    <div className={`${item.iconBg} p-2 rounded-full mr-4`}>
                                                        <LucideIcon className={`${item.iconColor} w-5 h-5`} />
                                                    </div>
                                                    <div className="flex-1">
                                                        <p className="text-sm font-medium text-gray-900">{item.title}</p>
                                                        <p className="text-sm text-gray-500">{item.description}</p>
                                                    </div>
                                                    <span className="text-xs text-gray-500">{item.time}</span>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                                <div className="px-6 py-3 bg-gray-50 text-center">
                                    <button className="text-indigo-600 hover:text-indigo-800 text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                        View All Activity
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Users */}
                    {activeTab === "users" && (
                        <div className="bg-white rounded-lg shadow">
                            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-gray-800">
                                    User KYC Verification
                                </h3>
                                <div className="flex space-x-2">
                                    <div className="relative">
                                        <input
                                            type="text"
                                            placeholder="Search users..."
                                            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                        />
                                        <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    </div>
                                    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer !rounded-button whitespace-nowrap">
                                        <i className="fas fa-filter mr-2"></i>
                                        Filter
                                    </button>
                                </div>
                            </div>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                User
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Email
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Registration Date
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                KYC Status
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {[
                                            {
                                                name: "John Smith",
                                                email: "<EMAIL>",
                                                date: "Apr 23, 2025",
                                                status: "Pending",
                                                statusColor: "yellow",
                                            },
                                            {
                                                name: "Emma Johnson",
                                                email: "<EMAIL>",
                                                date: "Apr 22, 2025",
                                                status: "Verified",
                                                statusColor: "green",
                                            },
                                            {
                                                name: "Michael Brown",
                                                email: "<EMAIL>",
                                                date: "Apr 21, 2025",
                                                status: "Pending",
                                                statusColor: "yellow",
                                            },
                                            {
                                                name: "Sarah Davis",
                                                email: "<EMAIL>",
                                                date: "Apr 20, 2025",
                                                status: "Rejected",
                                                statusColor: "red",
                                            },
                                            {
                                                name: "Robert Wilson",
                                                email: "<EMAIL>",
                                                date: "Apr 19, 2025",
                                                status: "Verified",
                                                statusColor: "green",
                                            },
                                        ].map((user, index) => (
                                            <tr key={index}>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                            <span className="text-gray-600 font-medium">
                                                                {user.name.charAt(0)}
                                                            </span>
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {user.name}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {user.email}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {user.date}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span
                                                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${user.statusColor}-100 text-${user.statusColor}-800`}
                                                    >
                                                        {user.status}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="relative group">
                                                        <button className="text-gray-600 hover:text-gray-900 cursor-pointer !rounded-button whitespace-nowrap">
                                                            <MoreVertical className="w-5 h-5 text-gray-600" />
                                                        </button>
                                                        <div className="absolute right-0 mt-2 w-56 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 bg-white rounded-lg shadow-lg z-50">
                                                            <div className="py-1">
                                                                <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center !rounded-button whitespace-nowrap">
                                                                    <Eye className="w-5 h-5 text-gray-700 mr-1" />
                                                                    <span>View Details</span>
                                                                </button>
                                                                <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center !rounded-button whitespace-nowrap">
                                                                    <Edit className="w-5 h-5 text-gray-700 mr-1" />
                                                                    <span>Edit Profile</span>
                                                                </button>
                                                                <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center !rounded-button whitespace-nowrap">
                                                                    <Key className="w-5 h-5 text-gray-700 mr-1" />
                                                                    <span>Reset Password</span>
                                                                </button>
                                                                <button className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center !rounded-button whitespace-nowrap">
                                                                    <UserCheck className="w-5 h-5 text-gray-700 mr-1" />
                                                                    <span>Change KYC Status</span>
                                                                </button>
                                                                <button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        const dialog = document.getElementById(
                                                                            "deleteConfirmDialog",
                                                                        );
                                                                        if (dialog)
                                                                            dialog.classList.remove("hidden");
                                                                    }}
                                                                    className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center !rounded-button whitespace-nowrap"
                                                                >
                                                                    <Trash2 className="w-5 h-5 mr-1" />
                                                                    <span>Delete User</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                <div className="text-sm text-gray-500">
                                    Showing <span className="font-medium">1</span> to{" "}
                                    <span className="font-medium">5</span> of{" "}
                                    <span className="font-medium">42</span> results
                                </div>
                                <div className="flex space-x-2">
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        Previous
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                        1
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        2
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        3
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Deposits */}
                    {activeTab === "deposits" && (
                        <div className="bg-white rounded-lg shadow">
                            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-gray-800">
                                    Deposit Verification
                                </h3>
                                <div className="flex space-x-2">
                                    <div className="relative">
                                        <input
                                            type="text"
                                            placeholder="Search deposits..."
                                            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                        />
                                        <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    </div>
                                    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer !rounded-button whitespace-nowrap">
                                        <i className="fas fa-filter mr-2"></i>
                                        Filter
                                    </button>
                                </div>
                            </div>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                User
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Amount
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Date
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Transaction ID
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Status
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {[
                                            {
                                                name: "Emma Johnson",
                                                amount: "$5,000",
                                                date: "Apr 29, 2025 - 10:23 AM",
                                                txId: "TXN-38492-ADFK2",
                                                status: "Pending",
                                                statusColor: "yellow",
                                            },
                                            {
                                                name: "Michael Brown",
                                                amount: "$2,500",
                                                date: "Apr 28, 2025 - 3:45 PM",
                                                txId: "TXN-38491-BHJL7",
                                                status: "Verified",
                                                statusColor: "green",
                                            },
                                            {
                                                name: "Sarah Davis",
                                                amount: "$10,000",
                                                date: "Apr 28, 2025 - 11:30 AM",
                                                txId: "TXN-38490-CDFG9",
                                                status: "Pending",
                                                statusColor: "yellow",
                                            },
                                            {
                                                name: "Robert Wilson",
                                                amount: "$1,000",
                                                date: "Apr 27, 2025 - 5:15 PM",
                                                txId: "TXN-38489-DERT5",
                                                status: "Rejected",
                                                statusColor: "red",
                                            },
                                            {
                                                name: "Jennifer Lee",
                                                amount: "$7,500",
                                                date: "Apr 27, 2025 - 9:20 AM",
                                                txId: "TXN-38488-FGTY6",
                                                status: "Verified",
                                                statusColor: "green",
                                            },
                                        ].map((deposit, index) => (
                                            <tr key={index}>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {deposit.name}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {deposit.amount}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {deposit.date}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {deposit.txId}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span
                                                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${deposit.statusColor}-100 text-${deposit.statusColor}-800`}
                                                    >
                                                        {deposit.status}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="flex space-x-2">
                                                        <button className="text-indigo-600 hover:text-indigo-900 cursor-pointer !rounded-button whitespace-nowrap">
                                                            <Eye className="w-5 h-5 " />
                                                        </button>
                                                        {deposit.status === "Pending" && (
                                                            <>
                                                                <button className="text-green-600 hover:text-green-900 cursor-pointer !rounded-button whitespace-nowrap">
                                                                    <Check className="w-5 h-5" />
                                                                </button>
                                                                <button className="text-red-600 hover:text-red-900 cursor-pointer !rounded-button whitespace-nowrap">
                                                                    <X className="w-5 h-5 text-red-600" />
                                                                </button>
                                                            </>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                <div className="text-sm text-gray-500">
                                    Showing <span className="font-medium">1</span> to{" "}
                                    <span className="font-medium">5</span> of{" "}
                                    <span className="font-medium">28</span> results
                                </div>
                                <div className="flex space-x-2">
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        Previous
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                        1
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        2
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        3
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Withdrawals */}
                    {activeTab === "withdrawals" && (
                        <div className="bg-white rounded-lg shadow">
                            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-gray-800">
                                    Withdrawal Requests
                                </h3>
                                <div className="flex space-x-2">
                                    <div className="relative">
                                        <input
                                            type="text"
                                            placeholder="Search withdrawals..."
                                            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                        />
                                        <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                    </div>
                                    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer !rounded-button whitespace-nowrap">
                                        <i className="fas fa-filter mr-2"></i>
                                        Filter
                                    </button>
                                </div>
                            </div>
                            <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div className="bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg p-4 text-white">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm opacity-75">
                                            Total Withdrawals
                                        </span>
                                        <Wallet className="w-5 h-5" />
                                    </div>
                                    <div className="text-2xl font-bold">USDT 425,890</div>
                                    <div className="text-xs mt-2">
                                        <span className="opacity-75">+12.5% from last month</span>
                                    </div>
                                </div>
                                <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm opacity-75">Pending Requests</span>
                                        <Clock className="w-5 h-5 " />
                                    </div>
                                    <div className="text-2xl font-bold">23</div>
                                    <div className="text-xs mt-2">
                                        <span className="opacity-75">Updated 5 minutes ago</span>
                                    </div>
                                </div>
                                <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm opacity-75">Approved Today</span>
                                        <CheckCircle className="w-5 h-5" />
                                    </div>
                                    <div className="text-2xl font-bold">15</div>
                                    <div className="text-xs mt-2">
                                        <span className="opacity-75">$52,500 total amount</span>
                                    </div>
                                </div>
                                <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-4 text-white">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm opacity-75">Rejected Today</span>
                                        <XCircle className="w-5 h-5" />
                                    </div>
                                    <div className="text-2xl font-bold">3</div>
                                    <div className="text-xs mt-2">
                                        <span className="opacity-75">Due to KYC issues</span>
                                    </div>
                                </div>
                            </div>
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                User
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Amount
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Wallet/Bank Details
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Date
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Reference ID
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Status
                                            </th>
                                            <th
                                                scope="col"
                                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                            >
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {[
                                            {
                                                name: "James Wilson",
                                                amount: "$3,500",
                                                wallet: "USDT TRC20: TJkE9k...x8F2",
                                                date: "Apr 29, 2025 - 11:45 AM",
                                                refId: "WDR-********-001",
                                                status: "Pending",
                                                statusColor: "yellow",
                                                kyc: "Verified",
                                            },
                                            {
                                                name: "Sophie Chen",
                                                amount: "$12,000",
                                                wallet: "USDT ERC20: 0x8F3c...9A2",
                                                date: "Apr 29, 2025 - 10:30 AM",
                                                refId: "WDR-********-002",
                                                status: "Pending",
                                                statusColor: "yellow",
                                                kyc: "Verified",
                                            },
                                            {
                                                name: "Alex Thompson",
                                                amount: "$5,000",
                                                wallet: "Bank Transfer",
                                                date: "Apr 29, 2025 - 09:15 AM",
                                                refId: "WDR-********-003",
                                                status: "Approved",
                                                statusColor: "green",
                                                kyc: "Verified",
                                            },
                                            {
                                                name: "Maria Garcia",
                                                amount: "$8,500",
                                                wallet: "USDT TRC20: TWr5Ns...p7H",
                                                date: "Apr 29, 2025 - 08:20 AM",
                                                refId: "WDR-********-004",
                                                status: "Rejected",
                                                statusColor: "red",
                                                kyc: "Unverified",
                                            },
                                            {
                                                name: "David Kim",
                                                amount: "$2,300",
                                                wallet: "Bank Transfer",
                                                date: "Apr 29, 2025 - 07:45 AM",
                                                refId: "WDR-********-005",
                                                status: "Approved",
                                                statusColor: "green",
                                                kyc: "Verified",
                                            },
                                        ].map((withdrawal, index) => (
                                            <tr key={index} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                            <span className="text-indigo-600 font-medium">
                                                                {withdrawal.name.charAt(0)}
                                                            </span>
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {withdrawal.name}
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                KYC: {withdrawal.kyc}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {withdrawal.amount}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500 max-w-xs truncate">
                                                        {withdrawal.wallet}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {withdrawal.date}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-500">
                                                        {withdrawal.refId}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span
                                                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${withdrawal.statusColor}-100 text-${withdrawal.statusColor}-800`}
                                                    >
                                                        {withdrawal.status}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div className="flex space-x-3">
                                                        <button className="text-indigo-600 hover:text-indigo-900 !rounded-button whitespace-nowrap">
                                                            <i className="fas fa-eye"></i>
                                                        </button>
                                                        {withdrawal.status === "Pending" && (
                                                            <>
                                                                <button className="text-green-600 hover:text-green-900 !rounded-button whitespace-nowrap">
                                                                    <i className="fas fa-check"></i>
                                                                </button>
                                                                <button className="text-red-600 hover:text-red-900 !rounded-button whitespace-nowrap">
                                                                    <i className="fas fa-times"></i>
                                                                </button>
                                                            </>
                                                        )}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                <div className="text-sm text-gray-500">
                                    Showing <span className="font-medium">1</span> to{" "}
                                    <span className="font-medium">5</span> of{" "}
                                    <span className="font-medium">25</span> results
                                </div>
                                <div className="flex space-x-2">
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        Previous
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                        1
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        2
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        3
                                    </button>
                                    <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Profit Management */}
                    {activeTab === "profit" && (
                        <div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm mb-1">
                                                Today's Profit Rate
                                            </p>
                                            <h3 className="text-2xl font-bold text-gray-800">2.4%</h3>
                                        </div>
                                        <div className="bg-green-100 p-3 rounded-full">
                                            <LineChart className="text-green-600 w-5 h-5" />
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm mb-1">
                                                Total Users Receiving Profit
                                            </p>
                                            <h3 className="text-2xl font-bold text-gray-800">
                                                1,248
                                            </h3>
                                        </div>
                                        <div className="bg-blue-100 p-3 rounded-full">
                                            <Users className="text-blue-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm mb-1">
                                                Total Net Deposits
                                            </p>
                                            <h3 className="text-2xl font-bold text-gray-800">
                                                $2.5M
                                            </h3>
                                        </div>
                                        <div className="bg-purple-100 p-3 rounded-full">
                                            <Banknote className="text-purple-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white p-6 rounded-lg shadow">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-gray-500 text-sm mb-1">
                                                Today's Distribution Amount
                                            </p>
                                            <h3 className="text-2xl font-bold text-gray-800">
                                                $60,000
                                            </h3>
                                        </div>
                                        <div className="bg-indigo-100 p-3 rounded-full">
                                            <HandCoins className="text-indigo-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-white rounded-lg shadow mb-6">
                                <div className="p-6 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                                        Set Daily Profit Rate
                                    </h3>
                                    <form className="flex items-end gap-4">
                                        <div className="flex-1 max-w-xs">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Profit Percentage
                                            </label>
                                            <div className="relative">
                                                <input
                                                    type="number"
                                                    step="0.1"
                                                    min="0"
                                                    max="100"
                                                    placeholder="Enter percentage"
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                                />
                                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                                                    %
                                                </span>
                                            </div>
                                        </div>
                                        <button
                                            type="submit"
                                            className="px-6 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 !rounded-button whitespace-nowrap"
                                        >
                                            Publish Daily Profit
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Profit Distribution History
                                    </h3>
                                    <div className="flex space-x-2">
                                        <div className="relative">
                                            <input
                                                type="text"
                                                placeholder="Search entries..."
                                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            />
                                            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                        </div>
                                        <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 !rounded-button whitespace-nowrap">
                                            <i className="fas fa-filter mr-2"></i>
                                            Filter
                                        </button>
                                    </div>
                                </div>
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Date
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Profit Rate
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Total Amount
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Recipients
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Status
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {[
                                                {
                                                    date: "Apr 29, 2025 - 10:00 AM",
                                                    rate: "2.4%",
                                                    amount: "$60,000",
                                                    recipients: "1,248",
                                                    status: "Completed",
                                                    statusColor: "green",
                                                },
                                                {
                                                    date: "Apr 28, 2025 - 10:00 AM",
                                                    rate: "2.2%",
                                                    amount: "$55,000",
                                                    recipients: "1,235",
                                                    status: "Completed",
                                                    statusColor: "green",
                                                },
                                                {
                                                    date: "Apr 27, 2025 - 10:00 AM",
                                                    rate: "2.5%",
                                                    amount: "$62,500",
                                                    recipients: "1,220",
                                                    status: "Completed",
                                                    statusColor: "green",
                                                },
                                                {
                                                    date: "Apr 26, 2025 - 10:00 AM",
                                                    rate: "2.3%",
                                                    amount: "$57,500",
                                                    recipients: "1,210",
                                                    status: "Completed",
                                                    statusColor: "green",
                                                },
                                                {
                                                    date: "Apr 25, 2025 - 10:00 AM",
                                                    rate: "2.1%",
                                                    amount: "$52,500",
                                                    recipients: "1,200",
                                                    status: "Completed",
                                                    statusColor: "green",
                                                },
                                            ].map((entry, index) => (
                                                <tr key={index} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {entry.date}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {entry.rate}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {entry.amount}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {entry.recipients}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${entry.statusColor}-100 text-${entry.statusColor}-800`}
                                                        >
                                                            {entry.status}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex space-x-3">
                                                            <button className="text-indigo-600 hover:text-indigo-900 !rounded-button whitespace-nowrap">
                                                                <Eye className="w-5 h-5" />
                                                            </button>
                                                            <button className="text-gray-600 hover:text-gray-900 !rounded-button whitespace-nowrap">
                                                                <Edit className="w-5 h-5 " />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                    <div className="text-sm text-gray-500">
                                        Showing <span className="font-medium">1</span> to{" "}
                                        <span className="font-medium">5</span> of{" "}
                                        <span className="font-medium">30</span> results
                                    </div>
                                    <div className="flex space-x-2">
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap">
                                            Previous
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium !rounded-button whitespace-nowrap">
                                            1
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap">
                                            2
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap">
                                            3
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 !rounded-button whitespace-nowrap">
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {/* Placeholder for other tabs */}
                    {activeTab === "currency" && (
                        <div>
                            <div className="bg-white rounded-lg shadow mb-6">
                                <div className="p-6 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                                        Add New Currency
                                    </h3>
                                    <form className="flex items-end gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Currency
                                            </label>
                                            <div className="relative">
                                                <button
                                                    type="button"
                                                    className="w-48 px-4 py-2 border border-gray-300 rounded-lg text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm !rounded-button whitespace-nowrap"
                                                >
                                                    <span className="flex items-center justify-between">
                                                        <span>Select Currency</span>
                                                        <i className="fas fa-chevron-down"></i>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                        <div className="flex-1 max-w-xs">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                USDT Exchange Rate
                                            </label>
                                            <div className="relative">
                                                <input
                                                    type="number"
                                                    step="0.0001"
                                                    min="0"
                                                    placeholder="Enter rate"
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                                />
                                                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                                                    USDT
                                                </span>
                                            </div>
                                        </div>
                                        <button
                                            type="submit"
                                            className="px-6 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 !rounded-button whitespace-nowrap"
                                        >
                                            Add Currency
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Currency List
                                    </h3>
                                    <div className="flex space-x-2">
                                        <div className="relative">
                                            <input
                                                type="text"
                                                placeholder="Search currencies..."
                                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            />
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                        </div>
                                    </div>
                                </div>
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Currency
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    USDT Exchange Rate
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Last Updated
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Status
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {[
                                                {
                                                    currency: "USD",
                                                    code: "USD",
                                                    flag: "us",
                                                    rate: "1.0000",
                                                    lastUpdated: "Apr 29, 2025 - 10:00 AM",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    currency: "Indian Rupee",
                                                    code: "INR",
                                                    flag: "in",
                                                    rate: "82.4530",
                                                    lastUpdated: "Apr 29, 2025 - 09:45 AM",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    currency: "UAE Dirham",
                                                    code: "AED",
                                                    flag: "ae",
                                                    rate: "3.6725",
                                                    lastUpdated: "Apr 29, 2025 - 09:30 AM",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    currency: "Euro",
                                                    code: "EUR",
                                                    flag: "eu",
                                                    rate: "0.9150",
                                                    lastUpdated: "Apr 29, 2025 - 09:15 AM",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    currency: "British Pound",
                                                    code: "GBP",
                                                    flag: "gb",
                                                    rate: "0.7950",
                                                    lastUpdated: "Apr 29, 2025 - 09:00 AM",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                            ].map((currency, index) => (
                                                <tr key={index} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <Flag className="mr-2 text-gray-400 w-5 h-5" />
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {currency.currency} ({currency.code})
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            1 {currency.code} = {currency.rate} USDT
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-500">
                                                            {currency.lastUpdated}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${currency.statusColor}-100 text-${currency.statusColor}-800`}
                                                        >
                                                            {currency.status}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex space-x-3">
                                                            <button className="text-indigo-600 hover:text-indigo-900 !rounded-button whitespace-nowrap">
                                                                <Edit className="w-5 h-5" />
                                                            </button>
                                                            <button onClick={() => setEnabled(!enabled)}>
                                                                {enabled ? (
                                                                    <ToggleRight className="w-6 h-6 text-green-600" />
                                                                ) : (
                                                                    <ToggleLeft className="w-6 h-6 text-gray-400" />
                                                                )}
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                    <div className="text-sm text-gray-500">
                                        Showing <span className="font-medium">1</span> to{" "}
                                        <span className="font-medium">5</span> of{" "}
                                        <span className="font-medium">5</span> results
                                    </div>
                                    <div className="flex space-x-2">
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                            Previous
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                            1
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {activeTab === "deposit-slabs" && (
                        <div>
                            <div className="bg-white rounded-lg shadow mb-6">
                                <div className="p-6 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                                        Add New Deposit Slab
                                    </h3>
                                    <form className="flex items-end gap-4">
                                        <div className="flex-1 max-w-xs">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Deposit Amount (USDT)
                                            </label>
                                            <input
                                                type="number"
                                                min="0"
                                                placeholder="Enter amount"
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            />
                                        </div>
                                        <div className="flex-1 max-w-xs">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Deposit Name
                                            </label>
                                            <input
                                                type="text"
                                                placeholder="Enter name"
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            />
                                        </div>
                                        <button
                                            type="submit"
                                            className="px-6 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 !rounded-button whitespace-nowrap"
                                        >
                                            Add Deposit Slab
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Deposit Slabs
                                    </h3>
                                    <div className="flex space-x-2">
                                        <div className="relative">
                                            <input
                                                type="text"
                                                placeholder="Search slabs..."
                                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            />
                                            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Name
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Amount (USDT)
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Created Date
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Status
                                                </th>
                                                <th
                                                    scope="col"
                                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                                                >
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {[
                                                {
                                                    name: "Becath",
                                                    amount: "2,500",
                                                    date: "Apr 29, 2025",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    name: "Silver",
                                                    amount: "5,000",
                                                    date: "Apr 28, 2025",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    name: "Gold",
                                                    amount: "10,000",
                                                    date: "Apr 27, 2025",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                                {
                                                    name: "Platinum",
                                                    amount: "25,000",
                                                    date: "Apr 26, 2025",
                                                    status: "Inactive",
                                                    statusColor: "red",
                                                },
                                                {
                                                    name: "Diamond",
                                                    amount: "50,000",
                                                    date: "Apr 25, 2025",
                                                    status: "Active",
                                                    statusColor: "green",
                                                },
                                            ].map((slab, index) => (
                                                <tr key={index} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {slab.name}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            ${slab.amount}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-500">
                                                            {slab.date}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span
                                                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${slab.statusColor}-100 text-${slab.statusColor}-800`}
                                                        >
                                                            {slab.status}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex space-x-3">
                                                            <button className="text-indigo-600 hover:text-indigo-900 !rounded-button whitespace-nowrap">
                                                                <Edit className="w-5 h-5" />
                                                            </button>
                                                            <button className="text-red-600 hover:text-red-900 !rounded-button whitespace-nowrap">
                                                                <Trash2 className="w-5 h-5" />
                                                            </button>
                                                            <button className="text-gray-600 hover:text-gray-900 !rounded-button whitespace-nowrap">
                                                                <ToggleRight className="w-5 h-5" />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                    <div className="text-sm text-gray-500">
                                        Showing <span className="font-medium">1</span> to{" "}
                                        <span className="font-medium">5</span> of{" "}
                                        <span className="font-medium">5</span> results
                                    </div>
                                    <div className="flex space-x-2">
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                            Previous
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                            1
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                    {activeTab !== "dashboard" &&
                        activeTab !== "users" &&
                        activeTab !== "deposits" &&
                        activeTab !== "withdrawals" &&
                        activeTab !== "profit" &&
                        activeTab !== "currency" &&
                        activeTab !== "deposit-slabs" &&
                        activeTab !== "products" && (
                            <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-center min-h-[600px]">
                                <div className="text-6xl text-indigo-200 mb-4">
                                    <i className="fas fa-tools"></i>
                                </div>
                                <h3 className="text-2xl font-semibold text-gray-700 mb-2">
                                    Coming Soon
                                </h3>
                                <p className="text-gray-500 text-center max-w-md">
                                    This section is currently under development. Please check back
                                    later for updates.
                                </p>
                            </div>
                        )}
                    {activeTab === "products" && (
                        <div>
                            <div className="bg-white rounded-lg shadow mb-6">
                                <div className="p-6 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                                        Add New Product
                                    </h3>
                                    <form className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Product Name
                                                </label>
                                                <input
                                                    type="text"
                                                    placeholder="Enter product name"
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Product Image
                                                </label>
                                                <div className="flex items-center">
                                                    <div className="flex-1">
                                                        <div className="relative border border-gray-300 rounded-lg px-4 py-2 bg-white">
                                                            <input
                                                                type="file"
                                                                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                                                accept="image/*"
                                                            />
                                                            <div className="flex items-center text-gray-500 text-sm">
                                                                <CloudUpload className="mr-2 w-5 h-5 text-gray-700" />
                                                                <span>Choose file or drag & drop</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Description
                                            </label>
                                            <textarea
                                                placeholder="Enter product description"
                                                rows={4}
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            ></textarea>
                                        </div>
                                        <div className="flex justify-end">
                                            <button
                                                type="submit"
                                                className="px-6 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 !rounded-button whitespace-nowrap"
                                            >
                                                Add Product
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div className="bg-white rounded-lg shadow">
                                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <h3 className="text-lg font-semibold text-gray-800">
                                        Product List
                                    </h3>
                                    <div className="flex space-x-2">
                                        <div className="relative">
                                            <input
                                                type="text"
                                                placeholder="Search products..."
                                                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                                            />
                                            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                                    {[
                                        {
                                            id: 1,
                                            name: "Avocados",
                                            description:
                                                "The world's first cryptocurrency, operating on a decentralized blockchain network.",
                                            image:
                                                "https://images.healthshots.com/healthshots/en/uploads/2024/04/04153309/avocado-1.jpg",
                                            status: "Active",
                                        },
                                        {
                                            id: 2,
                                            name: "Apple",
                                            description:
                                                "A leading technology company known for its innovative products including iPhone, Mac, and services.",
                                            image:
                                                "https://plus.unsplash.com/premium_photo-1661322640130-f6a1e2c36653?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YXBwbGV8ZW58MHx8MHx8fDA%3D",
                                            status: "Active",
                                        },
                                        {
                                            id: 3,
                                            name: "Bananas",
                                            description:
                                                "The most traded currency pair representing the exchange rate between the Euro and US Dollar.",
                                            image:
                                                "https://media.istockphoto.com/id/1184345169/photo/banana.jpg?s=612x612&w=0&k=20&c=NdHyi6Jd9y1855Q5mLO2tV_ZRnaJGtZGCSMMT7oxdF4=",
                                            status: "Active",
                                        },
                                        {
                                            id: 4,
                                            name: "Figs",
                                            description:
                                                "A precious metal used as a store of value and hedge against inflation.",
                                            image:
                                                "https://media.istockphoto.com/id/1378747279/photo/fresh-fig-fruit-and-slices-of-figs-background.jpg?s=612x612&w=0&k=20&c=XWqPYMQu13LUJUiwyNZ3tZCyarFlyRBwLWio_4mFARM=",
                                            status: "Active",
                                        },
                                    ].map((product, index) => (
                                        <div
                                            key={index}
                                            className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300"
                                        >
                                            <div className="aspect-w-4 aspect-h-3 relative">
                                                <div className="h-48 overflow-hidden">
                                                    <img
                                                        src={product.image}
                                                        alt={product.name}
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>

                                                <div className="absolute top-2 right-2">
                                                    <span
                                                        className={`px-2 py-1 text-xs font-semibold rounded-full ${product.status === "Active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                                                    >
                                                        {product.status}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="p-4">
                                                <h4 className="text-lg font-medium text-gray-900 mb-2">
                                                    {product.name}
                                                </h4>
                                                <p className="text-sm text-gray-500 mb-4">
                                                    {product.description}
                                                </p>
                                                <div className="flex justify-end space-x-2">
                                                    <button className="text-indigo-600 hover:text-indigo-900 !rounded-button whitespace-nowrap">
                                                        <Edit className="w-5 h-5" />
                                                    </button>
                                                    <button className="text-red-600 hover:text-red-900 !rounded-button whitespace-nowrap">
                                                        <Trash2 className="w-5 h-5" />
                                                    </button>
                                                    <button className="text-gray-600 hover:text-gray-900 !rounded-button whitespace-nowrap">
                                                        <ToggleRight className="w-5 h-5" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                    <div className="text-sm text-gray-500">
                                        Showing <span className="font-medium">1</span> to{" "}
                                        <span className="font-medium">5</span> of{" "}
                                        <span className="font-medium">5</span> results
                                    </div>
                                    <div className="flex space-x-2">
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                            Previous
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 bg-indigo-50 text-indigo-600 rounded-md text-sm font-medium cursor-pointer !rounded-button whitespace-nowrap">
                                            1
                                        </button>
                                        <button className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 cursor-pointer !rounded-button whitespace-nowrap">
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </main>
            </div>
        </div>
    );
};
export default AdminDashboard;
