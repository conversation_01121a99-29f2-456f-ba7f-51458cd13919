// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";
import { X } from "lucide-react";

interface Product {
  id: number;
  name: string;
  description: string;
  image: string;
}

const Products: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const products = [
    {
      id: 1,
      name: "Avocados",
      description:
        "The world's first cryptocurrency, operating on a decentralized blockchain network.",
      image:
        "https://images.healthshots.com/healthshots/en/uploads/2024/04/04153309/avocado-1.jpg",
    },
    {
      id: 2,
      name: "Apple",
      description:
        "A leading technology company known for its innovative products including iPhone, Mac, and services.",
      image:
        "https://plus.unsplash.com/premium_photo-1661322640130-f6a1e2c36653?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YXBwbGV8ZW58MHx8MHx8fDA%3D",
    },
    {
      id: 3,
      name: "Bananas",
      description:
        "The most traded currency pair representing the exchange rate between the Euro and US Dollar.",
      image:
        "https://media.istockphoto.com/id/**********/photo/banana.jpg?s=612x612&w=0&k=20&c=NdHyi6Jd9y1855Q5mLO2tV_ZRnaJGtZGCSMMT7oxdF4=",
    },
    {
      id: 4,
      name: "Figs",
      description:
        "A precious metal used as a store of value and hedge against inflation.",
      image:
        "https://media.istockphoto.com/id/**********/photo/fresh-fig-fruit-and-slices-of-figs-background.jpg?s=612x612&w=0&k=20&c=XWqPYMQu13LUJUiwyNZ3tZCyarFlyRBwLWio_4mFARM=",
    },
  ];

  return (
    <div className="min-h-screen bg-[#0F1419] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Trading Products
          </h1>
          <p className="text-xl text-[#A1A1AA]">
            Discover our selection of premium trading products designed for
            modern investors
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-[#1A1D24] border border-[#2A2F3A] rounded-lg shadow-lg overflow-hidden hover:shadow-xl hover:shadow-[#4C7BF4]/10 transition-all duration-300 hover:border-[#4C7BF4]/30 group"
            >
              <div className="h-48 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-[#4C7BF4] transition-colors duration-200">
                  {product.name}
                </h3>
                <p className="text-[#A1A1AA] text-sm line-clamp-2 mb-4 leading-relaxed">
                  {product.description}
                </p>
                <button
                  onClick={() => setSelectedProduct(product)}
                  className="inline-flex items-center px-4 py-2 bg-[#4C7BF4] text-white text-sm font-medium rounded-lg hover:bg-[#3B6DE8] transition-all duration-200 shadow-md hover:shadow-lg hover:shadow-[#4C7BF4]/20"
                >
                  Read More
                </button>
              </div>
            </div>
          ))}
        </div>

        {selectedProduct && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-[#1A1D24] border border-[#2A2F3A] rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <div className="relative">
                <img
                  src={selectedProduct.image}
                  alt={selectedProduct.name}
                  className="w-full h-64 object-cover"
                />
                <button
                  onClick={() => setSelectedProduct(null)}
                  className="absolute top-4 right-4 bg-[#2A2F3A] hover:bg-[#4C7BF4] text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:shadow-xl border border-[#3A3F4A] hover:border-[#4C7BF4]"
                >
                  <X className="w-5 h-5" />
                </button>
                <div className="absolute inset-0 bg-gradient-to-t from-[#1A1D24]/80 via-transparent to-transparent pointer-events-none"></div>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold text-white mb-4">
                  {selectedProduct.name}
                </h3>
                <p className="text-[#A1A1AA] leading-relaxed text-base">
                  {selectedProduct.description}
                </p>
                <div className="mt-6 pt-4 border-t border-[#2A2F3A]">
                  <button
                    onClick={() => setSelectedProduct(null)}
                    className="inline-flex items-center px-6 py-3 bg-[#2A2F3A] hover:bg-[#4C7BF4] text-white font-medium rounded-lg transition-all duration-200 border border-[#3A3F4A] hover:border-[#4C7BF4] hover:shadow-lg"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Products;