// import React, { createContext, useContext, useState, ReactNode } from "react";
// import { UserData } from "./src/models/common.model";

// // Define the shape of the context
// interface GlobalContextProps {
//   isLoading: boolean;
//   setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
//   currentPage: string;
//   setCurrentPage: React.Dispatch<React.SetStateAction<string>>;
//   userData: UserData | undefined;
//   setUserData: React.Dispatch<React.SetStateAction<UserData | undefined>>;
//   isMenuExpanded: boolean;
//   setIsMenuExpanded: React.Dispatch<React.SetStateAction<boolean>>;
//   screenSize: string;
//   setScreenSize: React.Dispatch<React.SetStateAction<string>>;
//   maxDate: Date | undefined;
//   setMaxDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
// }

// // Create the context
// const GlobalContext = createContext<GlobalContextProps | undefined>(undefined);

// // Provider Component
// export const GlobalProvider = ({ children }: { children: ReactNode }) => {
//   const [isLoading, setIsLoading] = useState(false);
//   const [currentPage, setCurrentPage] = useState('');
//   const [userData, setUserData] = useState<UserData | undefined>();
//   const [isMenuExpanded, setIsMenuExpanded] = useState(false);
//   const [screenSize, setScreenSize] = useState('');
//   const [maxDate, setMaxDate] = useState<Date | undefined>();

//   return (
//     <GlobalContext.Provider value={{ 
//       isMenuExpanded, 
//       setIsMenuExpanded, 
//       isLoading, 
//       setIsLoading, 
//       currentPage, 
//       setCurrentPage, 
//       userData, 
//       setUserData, 
//       screenSize, 
//       setScreenSize, 
//       maxDate, 
//       setMaxDate 
//     }}>
//       {children}
//     </GlobalContext.Provider>
//   );
// };

// // Hook for easy access
// export const useGlobalContext = () => {
//   const context = useContext(GlobalContext);
//   if (!context) {
//     throw new Error("useGlobalContext must be used within a GlobalProvider");
//   }
//   return context;
// };