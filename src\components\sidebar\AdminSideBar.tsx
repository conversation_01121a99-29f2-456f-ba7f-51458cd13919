import { DollarSign, HandCoins, Layers, LayoutDashboard, LineChart, Package, Users, Wallet } from 'lucide-react';
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom';

function AdminSideBar() {
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState("dashboard");

    const handleTabChange = (tabId: string) => {
        setActiveTab(tabId);
        navigate(`/admin/${tabId}`);
    };

    const menuItems = [
        { id: "dashboard", label: "Dashboard", icon: LayoutDashboard },
        { id: "users", label: "Users", icon: Users },
        { id: "deposits", label: "Deposits", icon: Wallet },
        { id: "withdrawals", label: "Withdrawals", icon: HandCoins },
        { id: "profit", label: "Profit", icon: Line<PERSON>hart },
        { id: "currency", label: "Currency", icon: DollarSign },
        { id: "deposit-slabs", label: "Deposit Slabs", icon: Layers },
        { id: "products", label: "Products", icon: Package },
    ];

    return (
        <aside className="w-64 bg-[#1A1D24] border-r border-[#2A2F3A] fixed left-0 top-16 bottom-0 hidden md:block overflow-y-auto z-10">
            <nav className="mt-6 px-4">
                <div className="space-y-2">
                    {menuItems.map((item) => {
                        const IconComponent = item.icon;
                        return (
                            <button
                                key={item.id}
                                onClick={() => handleTabChange(item.id)}
                                className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out group ${
                                    activeTab === item.id 
                                        ? "bg-[#4C7BF4] text-white shadow-lg shadow-[#4C7BF4]/20" 
                                        : "text-[#A1A1AA] hover:bg-[#2A2F3A] hover:text-white hover:shadow-md"
                                }`}
                            >
                                <IconComponent 
                                    className={`w-5 h-5 mr-3 transition-all duration-200 ${
                                        activeTab === item.id 
                                            ? "text-white" 
                                            : "text-[#6B7280] group-hover:text-[#4C7BF4]"
                                    }`}
                                />
                                <span className="transition-all duration-200">{item.label}</span>
                            </button>
                        );
                    })}
                </div>
                
                {/* <div className="mt-10">
                    <div className="bg-gradient-to-br from-[#2A2F3A] to-[#1F2329] rounded-lg p-4 border border-[#3A3F4A] shadow-lg">
                        <h3 className="text-sm font-semibold text-white mb-2">
                            Admin Support
                        </h3>
                        <p className="text-xs text-[#A1A1AA] mb-4 leading-relaxed">
                            Contact technical support for admin panel assistance.
                        </p>
                        <div className="space-y-3">
                            <div className="flex items-center text-[#A1A1AA] group">
                                <div className="w-8 h-8 rounded-full bg-[#4C7BF4]/10 flex items-center justify-center mr-3 group-hover:bg-[#4C7BF4]/20 transition-colors duration-200">
                                    <svg className="w-4 h-4 text-[#4C7BF4]" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                </div>
                                <a
                                    href="mailto:<EMAIL>"
                                    className="text-sm hover:text-[#4C7BF4] transition-colors duration-200 font-medium"
                                >
                                    <EMAIL>
                                </a>
                            </div>
                            <div className="flex items-center text-[#A1A1AA] group">
                                <div className="w-8 h-8 rounded-full bg-[#4C7BF4]/10 flex items-center justify-center mr-3 group-hover:bg-[#4C7BF4]/20 transition-colors duration-200">
                                    <svg className="w-4 h-4 text-[#4C7BF4]" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.51 3.515"/>
                                    </svg>
                                </div>
                                <a
                                    href="https://wa.me/12345678900"
                                    className="text-sm hover:text-[#4C7BF4] transition-colors duration-200 font-medium"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    +1 (234) 567-8900
                                </a>
                            </div>
                        </div>
                    </div>
                </div> */}
            </nav>
        </aside>
    );
}

export default AdminSideBar;