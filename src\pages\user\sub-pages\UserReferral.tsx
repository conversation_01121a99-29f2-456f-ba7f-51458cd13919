// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect } from "react";
import * as echarts from "echarts";

const UserReferral: React.FC = () => {

  const [copySuccess, setCopySuccess] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const referralLink = "https://tradepro.com/ref/johndoe123";
  const referralStats = {
    totalReferrals: 42,
    activeReferrals: 28,
    totalEarnings: 1250.75,
    conversionRate: 66.7,
    avgEarningsPerReferral: 44.67,
  };
  const referredUsers = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      date: "Apr 25, 2025",
      status: "Active",
      earnings: 125.5,
    },
    {
      id: 2,
      name: "<PERSON> <PERSON>",
      email: "<EMAIL>",
      date: "Apr 22, 2025",
      status: "Active",
      earnings: 98.75,
    },
    {
      id: 3,
      name: "Carol Williams",
      email: "<EMAIL>",
      date: "Apr 18, 2025",
      status: "Inactive",
      earnings: 45.2,
    },
    {
      id: 4,
      name: "David Brown",
      email: "<EMAIL>",
      date: "Apr 15, 2025",
      status: "Active",
      earnings: 210.3,
    },
    {
      id: 5,
      name: "Emma Davis",
      email: "<EMAIL>",
      date: "Apr 10, 2025",
      status: "Active",
      earnings: 175.8,
    },
    {
      id: 6,
      name: "Frank Miller",
      email: "<EMAIL>",
      date: "Apr 05, 2025",
      status: "Inactive",
      earnings: 32.15,
    },
    {
      id: 7,
      name: "Grace Wilson",
      email: "<EMAIL>",
      date: "Mar 28, 2025",
      status: "Active",
      earnings: 145.6,
    },
    {
      id: 8,
      name: "Henry Taylor",
      email: "<EMAIL>",
      date: "Mar 20, 2025",
      status: "Active",
      earnings: 88.9,
    },
    {
      id: 9,
      name: "Ivy Anderson",
      email: "<EMAIL>",
      date: "Mar 15, 2025",
      status: "Active",
      earnings: 112.45,
    },
    {
      id: 10,
      name: "Jack Thomas",
      email: "<EMAIL>",
      date: "Mar 10, 2025",
      status: "Inactive",
      earnings: 28.7,
    },
  ];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };
  const filteredUsers = referredUsers
    .filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.date.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.earnings.toString().includes(searchTerm),
    )
    .sort((a, b) => {
      if (sortBy === "date") {
        return sortDirection === "asc"
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortBy === "earnings") {
        return sortDirection === "asc"
          ? a.earnings - b.earnings
          : b.earnings - a.earnings;
      } else if (sortBy === "name") {
        return sortDirection === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }
      return 0;
    });
  const itemsPerPage = 5;
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortDirection("asc");
    }
  };
  useEffect(() => {
    // Initialize referral tree chart
    const referralTreeElement = document.getElementById("referral-tree");
    if (referralTreeElement) {
      const referralTree = echarts.init(referralTreeElement);
      const treeData = {
        name: "You (Level 0)",
        value: ["Apr 1, 2025", "$0", "$0"],
        children: [
          {
            name: "Alice Smith (Level 1)",
            value: ["Apr 15, 2025", "$1,200", "$84"],
            children: [
              {
                name: "Bob Wilson (Level 2)",
                value: ["Apr 20, 2025", "$800", "$40"],
                children: [
                  {
                    name: "Carol Davis (Level 3)",
                    value: ["Apr 22, 2025", "$500", "$20"],
                    children: [
                      {
                        name: "David Brown (Level 4)",
                        value: ["Apr 25, 2025", "$300", "$7.5"],
                        children: [
                          {
                            name: "Eva White (Level 5)",
                            value: ["Apr 27, 2025", "$200", "$2"],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                name: "Frank Miller (Level 2)",
                value: ["Apr 21, 2025", "$600", "$30"],
              },
            ],
          },
          {
            name: "Grace Taylor (Level 1)",
            value: ["Apr 18, 2025", "$900", "$63"],
            children: [
              {
                name: "Henry Clark (Level 2)",
                value: ["Apr 23, 2025", "$400", "$20"],
              },
            ],
          },
        ],
      };
      const option = {
        animation: false,
        tooltip: {
          trigger: "item",
          formatter: function (params: any) {
            const value = params.data.value;
            return `
<div class="font-sans p-2">
<div class="font-medium">${params.data.name}</div>
<div class="text-sm mt-1">
<div>Joined: ${value[0]}</div>
<div>Total Deposited: ${value[1]}</div>
<div>Your Bonus: ${value[2]}</div>
</div>
</div>
`;
          },
        },
        series: [
          {
            type: "tree",
            data: [treeData],
            top: "5%",
            left: "10%",
            bottom: "5%",
            right: "10%",
            orient: "TB",
            symbolSize: 24,
            symbol: "roundRect",
            layout: "orthogonal",
            label: {
              position: "bottom",
              distance: 8,
              fontSize: 12,
              color: "#333",
              formatter: function (params: any) {
                return params.data.name;
              },
            },
            leaves: {
              label: {
                position: "bottom",
                distance: 8,
              },
            },
            emphasis: {
              focus: "descendant",
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
            initialTreeDepth: 3,
            lineStyle: {
              color: "#a5b4fc",
              width: 2,
              curveness: 0.5,
            },
            itemStyle: {
              color: "#fff",
              borderColor: "#4f46e5",
              borderWidth: 2,
            },
          },
        ],
      };
      referralTree.setOption(option);
      const handleResize = () => {
        referralTree.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        referralTree.dispose();
      };
    }
    // Initialize referral growth chart
    const referralChartElement = document.getElementById(
      "referral-growth-chart",
    );
    if (referralChartElement) {
      const referralChart = echarts.init(referralChartElement);
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
          formatter: "{b}: {c} referrals",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
          axisLine: {
            lineStyle: {
              color: "#ddd",
            },
          },
          axisLabel: {
            color: "#666",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#666",
          },
          splitLine: {
            lineStyle: {
              color: "#eee",
            },
          },
        },
        series: [
          {
            data: [5, 8, 12, 18, 25, 42],
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 8,
            itemStyle: {
              color: "#4f46e5",
            },
            lineStyle: {
              width: 3,
              color: "#4f46e5",
            },
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(79, 70, 229, 0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(79, 70, 229, 0.1)",
                  },
                ],
              },
            },
          },
        ],
      };
      referralChart.setOption(option);
      const handleResize = () => {
        referralChart.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        referralChart.dispose();
      };
    }
    // Initialize earnings chart
    const earningsChartElement = document.getElementById("earnings-chart");
    if (earningsChartElement) {
      const earningsChart = echarts.init(earningsChartElement);
      const option = {
        animation: false,
        tooltip: {
          trigger: "axis",
          formatter: "{b}: {c}",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
          axisLine: {
            lineStyle: {
              color: "#ddd",
            },
          },
          axisLabel: {
            color: "#666",
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#666",
            formatter: "{value}",
          },
          splitLine: {
            lineStyle: {
              color: "#eee",
            },
          },
        },
        series: [
          {
            data: [120, 210, 350, 480, 750, 1250.75],
            type: "bar",
            barWidth: "60%",
            itemStyle: {
              color: "#10b981",
            },
          },
        ],
      };
      earningsChart.setOption(option);
      const handleResize = () => {
        earningsChart.resize();
      };
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
        earningsChart.dispose();
      };
    }
  }, []);
  return (
    <div className="min-h-screen bg-[#0B1221] text-white">
      <main className="min-h-screen">
        <div className="p-6 space-y-6">
          <h1 className="text-3xl font-bold text-white">Referral Program</h1>
            {/* Referral Stats */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Referral Statistics */}
              <div className="lg:col-span-3 bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
                <h2 className="text-lg font-semibold text-white mb-4">
                  Referral Statistics
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-indigo-900/50 rounded-lg p-4 border border-indigo-800">
                    <p className="text-sm text-indigo-300 mb-1">
                      Total Referrals
                    </p>
                    <div className="flex items-center">
                      <i className="fas fa-users text-indigo-400 mr-2"></i>
                      <span className="text-2xl font-bold text-indigo-300">
                        {referralStats.totalReferrals}
                      </span>
                    </div>
                  </div>
                  <div className="bg-green-900/50 rounded-lg p-4 border border-green-800">
                    <p className="text-sm text-green-300 mb-1">
                      Active Referrals
                    </p>
                    <div className="flex items-center">
                      <i className="fas fa-user-check text-green-400 mr-2"></i>
                      <span className="text-2xl font-bold text-green-300">
                        {referralStats.activeReferrals}
                      </span>
                    </div>
                  </div>
                  <div className="bg-purple-900/50 rounded-lg p-4 border border-purple-800">
                    <p className="text-sm text-purple-300 mb-1">
                      Total Earnings
                    </p>
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <i className="fas fa-coins text-purple-400 mr-2"></i>
                        <span className="text-2xl font-bold text-purple-300">
                          {referralStats.totalEarnings.toFixed(2)} USDT
                        </span>
                      </div>
                      <div className="flex items-center mt-1">
                        <i className="fas fa-rupee-sign text-purple-400 mr-2"></i>
                        <span className="text-sm text-purple-400">
                          ≈ ₹{(referralStats.totalEarnings * 83.2).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Referral Tree View */}
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg shadow-md p-6 border border-slate-700 mb-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold text-white mb-2">
                    Your Referral Network
                  </h2>
                  <p className="text-sm text-gray-400">
                    Visualizing your multi-level referral network up to 5 levels
                    deep
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-emerald-500"></div>
                      <span className="ml-2 text-sm text-gray-400">Active</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-slate-600"></div>
                      <span className="ml-2 text-sm text-gray-400">
                        Inactive
                      </span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="px-4 py-2 text-sm bg-slate-700 border border-slate-600 text-gray-300 rounded-lg hover:bg-slate-600 transition-colors shadow-sm cursor-pointer">
                      <i className="fas fa-expand-arrows-alt mr-2"></i> Expand
                      All
                    </button>
                    <button className="px-4 py-2 text-sm bg-slate-700 border border-slate-600 text-gray-300 rounded-lg hover:bg-slate-600 transition-colors shadow-sm cursor-pointer">
                      <i className="fas fa-compress-arrows-alt mr-2"></i>{" "}
                      Collapse All
                    </button>
                  </div>
                </div>
              </div>
              <div className="bg-slate-800 rounded-lg shadow-sm p-4 border border-slate-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <select className="px-4 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <option value="all">All Levels</option>
                      <option value="1">Level 1</option>
                      <option value="2">Level 2</option>
                      <option value="3">Level 3</option>
                      <option value="4">Level 4</option>
                      <option value="5">Level 5</option>
                    </select>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-gray-400 hover:text-indigo-400 transition-colors cursor-pointer">
                        <i className="fas fa-search"></i>
                      </button>
                      <button className="p-2 text-gray-400 hover:text-indigo-400 transition-colors cursor-pointer">
                        <i className="fas fa-filter"></i>
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-indigo-400 transition-colors cursor-pointer">
                      <i className="fas fa-download"></i>
                    </button>
                    <button className="p-2 text-gray-400 hover:text-indigo-400 transition-colors cursor-pointer">
                      <i className="fas fa-print"></i>
                    </button>
                  </div>
                </div>
                <div id="referral-tree" className="h-[600px] w-full bg-slate-900 rounded-lg"></div>
              </div>
            </div>
            {/* Referral Link Section */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <h2 className="text-lg font-semibold text-white mb-4">
                Your Referral Link
              </h2>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <div className="mb-4">
                    <p className="text-sm text-gray-400 mb-2">
                      Share this link with friends to earn rewards:
                    </p>
                    <div className="flex items-center">
                      <input
                        type="text"
                        value={referralLink}
                        readOnly
                        className="flex-1 px-4 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-l-lg text-sm focus:ring-indigo-500 focus:border-indigo-500"
                      />
                      <button
                        onClick={() => copyToClipboard(referralLink)}
                        className="bg-indigo-600 text-white px-4 py-2 rounded-r-lg hover:bg-indigo-700 transition-colors cursor-pointer"
                      >
                        {copySuccess ? (
                          <i className="fas fa-check mr-1"></i>
                        ) : (
                          <i className="fas fa-copy mr-1"></i>
                        )}
                        {copySuccess ? "Copied!" : "Copy"}
                      </button>
                    </div>
                  </div>
                  <div className="mb-6">
                    <p className="text-sm text-gray-400 mb-3">Share via:</p>
                    <div className="flex space-x-3">
                      <button className="bg-[#1DA1F2] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-twitter"></i>
                      </button>
                      <button className="bg-[#4267B2] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-facebook-f"></i>
                      </button>
                      <button className="bg-[#0A66C2] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-linkedin-in"></i>
                      </button>
                      <button className="bg-[#25D366] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fab fa-whatsapp"></i>
                      </button>
                      <button className="bg-[#EA4335] text-white p-3 rounded-lg hover:bg-opacity-90 transition-colors cursor-pointer">
                        <i className="fas fa-envelope"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="bg-slate-700 p-2 border border-slate-600 rounded-lg shadow-sm mb-3">
                    <div className="bg-white p-1 rounded-lg mb-2">
                      <img
                        src="https://readdy.ai/api/search-image?query=QR%20code%20with%20modern%20design%2C%20clean%20black%20and%20white%20pattern%20on%20white%20background%2C%20high%20resolution%2C%20minimalist%20style%2C%20professional%20looking%20QR%20code%20with%20small%20logo%20in%20center&width=150&height=150&seq=1&orientation=squarish"
                        alt="Referral QR Code"
                        className="h-36 w-36 object-cover"
                      />
                    </div>
                  </div>
                  <button
                    onClick={() => setShowQRCode(!showQRCode)}
                    className="text-sm text-indigo-400 hover:text-indigo-300 cursor-pointer"
                  >
                    <i className="fas fa-download mr-1"></i> Download QR Code
                  </button>
                </div>
              </div>
            </div>
            {/* Rewards Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-blue-900 to-indigo-900 rounded-lg shadow-md p-6 border border-blue-800">
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-gray-800 rounded-full p-3">
                    <i className="fas fa-chart-network text-blue-400 text-xl"></i>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-lg font-semibold text-white mb-2">
                      Multi-Level Referral Program
                    </h2>
                    <p className="text-blue-200 mb-4">
                      Earn up to 19.5% of deposits across 5 levels! 💰
                    </p>
                    <div className="space-y-3">
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 1</h3>
                          <span className="text-lg font-bold text-white">
                            7%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Direct referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 2</h3>
                          <span className="text-lg font-bold text-white">
                            5%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Second-tier referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 3</h3>
                          <span className="text-lg font-bold text-white">
                            4%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Third-tier referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 4</h3>
                          <span className="text-lg font-bold text-white">
                            2.5%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Fourth-tier referral bonus
                        </p>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium">Level 5</h3>
                          <span className="text-lg font-bold text-white">
                            1%
                          </span>
                        </div>
                        <p className="text-blue-200 text-sm">
                          Fifth-tier referral bonus
                        </p>
                      </div>
                    </div>
                    <div className="mt-6 bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                      <div className="flex items-center">
                        <i className="fas fa-lightbulb text-yellow-400 mr-2"></i>
                        <p className="text-white text-sm">
                          Bonus applies every time your referred users make a
                          deposit!
                        </p>
                      </div>
                    </div>
                    <div className="flex mt-4">
                      <button
                        className="text-blue-300 hover:text-blue-200 text-sm underline cursor-pointer"
                      >
                        View full terms and conditions
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gradient-to-r from-indigo-900 to-purple-900 rounded-lg shadow-md p-6 border border-indigo-800">
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-gray-800 rounded-full p-3">
                    <i className="fas fa-crown text-indigo-400 text-xl"></i>
                  </div>
                  <div className="ml-4">
                    <h2 className="text-lg font-semibold text-white mb-2">
                      Membership Tiers
                    </h2>
                    <p className="text-indigo-200 mb-4">
                      Unlock exclusive benefits based on your referral network
                      and deposits!
                    </p>
                    <div className="grid grid-cols-1 gap-4">
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#CD7F32] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-medal text-[#CD7F32] mr-2"></i>{" "}
                            Bronze
                          </h3>
                          <span className="text-xs text-white bg-slate-700 px-2 py-1 rounded-full">
                            Current
                          </span>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>25 referrals</li>
                            <li>or ₹25,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#C0C0C0] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-medal text-[#C0C0C0] mr-2"></i>{" "}
                            Silver
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>50 referrals</li>
                            <li>or ₹50,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#FFD700] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-medal text-[#FFD700] mr-2"></i>{" "}
                            Gold
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>75 referrals</li>
                            <li>or ₹75,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                      <div className="bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-[#E5E4E2] border-opacity-50">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-white font-medium flex items-center">
                            <i className="fas fa-gem text-[#E5E4E2] mr-2"></i>{" "}
                            Platinum
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <p className="text-indigo-200 text-sm">
                            Minimum Requirements:
                          </p>
                          <ul className="text-indigo-200 text-sm list-disc list-inside space-y-1">
                            <li>100 referrals</li>
                            <li>or ₹1,00,000 deposited</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div className="mt-6 bg-slate-800/50 rounded-lg p-4 backdrop-blur-sm border border-slate-700">
                      <div className="flex items-center">
                        <i className="fas fa-info-circle text-yellow-400 mr-2"></i>
                        <p className="text-white text-sm">
                          Higher tiers unlock better rewards and exclusive
                          features!
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Referred Users Table */}
            <div className="bg-slate-800 rounded-lg shadow-md p-6 border border-slate-700">
              <h2 className="text-lg font-semibold text-white mb-4">
                Your Referred Users
              </h2>
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-slate-600 bg-slate-700 text-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-sm placeholder-gray-500"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fas fa-search text-gray-500"></i>
                  </div>
                </div>
                <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors flex items-center cursor-pointer">
                  <i className="fas fa-download mr-2"></i> Export Data
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-slate-700">
                  <thead className="bg-slate-900">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                        onClick={() => handleSort("name")}
                      >
                        <div className="flex items-center">
                          User
                          {sortBy === "name" && (
                            <i
                              className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                            ></i>
                          )}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                        onClick={() => handleSort("date")}
                      >
                        <div className="flex items-center">
                          Join Date
                          {sortBy === "date" && (
                            <i
                              className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                            ></i>
                          )}
                        </div>
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider cursor-pointer"
                        onClick={() => handleSort("earnings")}
                      >
                        <div className="flex items-center">
                          Earnings
                          {sortBy === "earnings" && (
                            <i
                              className={`fas fa-sort-${sortDirection === "asc" ? "up" : "down"} ml-1`}
                            ></i>
                          )}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-slate-800 divide-y divide-slate-700">
                    {paginatedUsers.length > 0 ? (
                      paginatedUsers.map((user) => (
                        <tr key={user.id} className="hover:bg-slate-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-800 flex items-center justify-center text-indigo-300 font-medium">
                                {user.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-white">
                                  {user.name}
                                </div>
                                <div className="text-sm text-gray-400">
                                  {user.email}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {user.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                user.status === "Active"
                                  ? "bg-green-800 text-green-300"
                                  : "bg-slate-700 text-gray-400"
                              }`}
                            >
                              {user.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                            ${user.earnings.toFixed(2)}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={4}
                          className="px-6 py-4 text-center text-sm text-gray-400"
                        >
                          No referred users found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-400">
                    Showing{" "}
                    <span className="font-medium">
                      {(currentPage - 1) * itemsPerPage + 1}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(
                        currentPage * itemsPerPage,
                        filteredUsers.length,
                      )}
                    </span>{" "}
                    of{" "}
                    <span className="font-medium">{filteredUsers.length}</span>{" "}
                    results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.max(prev - 1, 1))
                      }
                      disabled={currentPage === 1}
                      className={`px-3 py-1 rounded-md ${currentPage === 1 ? "bg-slate-700 text-gray-500 cursor-not-allowed" : "bg-slate-700 text-gray-300 hover:bg-slate-600 cursor-pointer"} border border-slate-600`}
                    >
                      Previous
                    </button>
                    <button
                      onClick={() =>
                        setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                      }
                      disabled={currentPage === totalPages}
                      className={`px-3 py-1 rounded-md ${currentPage === totalPages ? "bg-slate-700 text-gray-500 cursor-not-allowed" : "bg-slate-700 text-gray-300 hover:bg-slate-600 cursor-pointer"} border border-slate-600`}
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </div>
        </div>
      </main>
      {/* QR Code Modal */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Download QR Code
              </h3>
              <button
                onClick={() => setShowQRCode(false)}
                className="text-gray-400 hover:text-gray-600 cursor-pointer"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="flex justify-center mb-6">
              <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-sm">
                <img
                  src="https://readdy.ai/api/search-image?query=QR%20code%20with%20modern%20design%2C%20clean%20black%20and%20white%20pattern%20on%20white%20background%2C%20high%20resolution%2C%20minimalist%20style%2C%20professional%20looking%20QR%20code%20with%20small%20logo%20in%20center&width=250&height=250&seq=2&orientation=squarish"
                  alt="Referral QR Code Large"
                  className="h-64 w-64 object-cover"
                />
              </div>
            </div>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => setShowQRCode(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 cursor-pointer">
                <i className="fas fa-download mr-2"></i> Download
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default UserReferral;
