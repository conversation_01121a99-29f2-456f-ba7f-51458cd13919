import { useGlobalContext } from 'ContextGlobal';
import React from 'react';

function LoadingSpinner({ className = "" }) {
  const { isLoading } = useGlobalContext();
  
  if (!isLoading) return null;
  
  return (
    <div className={`absolute inset-0 flex items-center justify-center ${className}`}>
      <div className="flex flex-col items-center">
        <div className="w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-gray-600">
          Loading...
        </p>
      </div>
    </div>
  );
}

export default LoadingSpinner;