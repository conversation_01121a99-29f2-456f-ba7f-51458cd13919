// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from "react";

interface Product {
  id: number;
  name: string;
  description: string;
  image: string;
}

const App: React.FC = () => {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const products = [
    {
      id: 1,
      name: "Bitcoin",
      description:
        "The world's first cryptocurrency, operating on a decentralized blockchain network.",
      image:
        "https://readdy.ai/api/search-image?query=Bitcoin%20cryptocurrency%20gold%20coin%20with%20B%20symbol%20on%20dark%20background%2C%20professional%20product%20photography%20with%20soft%20lighting%20and%20reflections%2C%20ultra%20high%20quality%2C%20photorealistic%2C%20dark%20background&width=400&height=300&seq=3&orientation=landscape",
    },
    {
      id: 2,
      name: "Apple",
      description:
        "A leading technology company known for its innovative products including iPhone, Mac, and services.",
      image:
        "https://readdy.ai/api/search-image?query=Apple%20logo%20on%20minimalist%20dark%20background%2C%20professional%20product%20photography%20with%20soft%20lighting%2C%20ultra%20high%20quality%2C%20photorealistic%2C%20dark%20background&width=400&height=300&seq=4&orientation=landscape",
    },
    {
      id: 3,
      name: "EUR/USD",
      description:
        "The most traded currency pair representing the exchange rate between the Euro and US Dollar.",
      image:
        "https://readdy.ai/api/search-image?query=Euro%20and%20Dollar%20currency%20symbols%20on%20dark%20background%2C%20professional%20product%20photography%20with%20soft%20lighting%2C%20ultra%20high%20quality%2C%20photorealistic%2C%20dark%20background&width=400&height=300&seq=5&orientation=landscape",
    },
    {
      id: 4,
      name: "Gold",
      description:
        "A precious metal used as a store of value and hedge against inflation.",
      image:
        "https://readdy.ai/api/search-image?query=Gold%20bar%20or%20gold%20coin%20on%20dark%20background%2C%20professional%20product%20photography%20with%20soft%20lighting%20and%20reflections%2C%20ultra%20high%20quality%2C%20photorealistic%2C%20dark%20background&width=400&height=300&seq=6&orientation=landscape",
    },
  ];
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Trading Products
          </h1>
          <p className="text-xl text-gray-600">
            Discover our selection of premium trading products designed for
            modern investors
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
            >
              <div className="h-48 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {product.name}
                </h3>
                <p className="text-gray-600 text-sm line-clamp-2 mb-4">
                  {product.description}
                </p>
                <button
                  onClick={() => setSelectedProduct(product)}
                  className="text-blue-600 text-sm font-medium hover:text-blue-800 !rounded-button whitespace-nowrap"
                >
                  Read More
                </button>
              </div>
            </div>
          ))}
        </div>

        {selectedProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="relative">
                <img
                  src={selectedProduct.image}
                  alt={selectedProduct.name}
                  className="w-full h-64 object-cover"
                />
                <button
                  onClick={() => setSelectedProduct(null)}
                  className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 !rounded-button whitespace-nowrap"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                  {selectedProduct.name}
                </h3>
                <p className="text-gray-600">{selectedProduct.description}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default App;
