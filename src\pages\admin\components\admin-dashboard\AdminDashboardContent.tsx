import {
  ArrowDown,
  ArrowUp,
  Banknote,
  CheckCircle,
  DollarSign,
  HandCoins,
  LineChart,
  LucideIcon,
  UserPlus,
  Users,
} from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import * as echarts from "echarts";
import LoaderWithText from "../../../../components/loader/LoaderWithText";

function AdminDashboardContent() {
  const [isLoader, setIsLoader] = useState<boolean>(false);
  const userChartRef = useRef<HTMLDivElement>(null);
  const revenueChartRef = useRef<HTMLDivElement>(null);
  const userChartInstance = useRef<echarts.ECharts | null>(null);
  const revenueChartInstance = useRef<echarts.ECharts | null>(null);
  
  const iconMap: Record<string, LucideIcon> = {
    "fa-user-plus": UserPlus,
    "fa-money-bill-wave": Banknote,
    "fa-hand-holding-usd": Hand<PERSON>oins,
    "fa-check-circle": CheckCircle,
    "fa-chart-line": LineChart,
  };

  useEffect(() => {
    // Initialize charts after component mounts
    const initCharts = () => {
      if (userChartRef.current && !userChartInstance.current) {
        // User growth chart
        userChartInstance.current = echarts.init(userChartRef.current);
        userChartInstance.current.setOption({
          animation: false,
          backgroundColor: 'transparent',
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            backgroundColor: '#374151',
            borderColor: '#4B5563',
            textStyle: {
              color: '#F3F4F6'
            }
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              color: '#9CA3AF'
            },
            axisLine: {
              lineStyle: {
                color: '#4B5563'
              }
            }
          },
          yAxis: {
            type: "value",
            axisLabel: {
              color: '#9CA3AF'
            },
            axisLine: {
              lineStyle: {
                color: '#4B5563'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#374151'
              }
            }
          },
          series: [
            {
              name: "New Users",
              type: "bar",
              barWidth: "60%",
              data: [120, 200, 150, 180, 230, 270],
              itemStyle: {
                color: "#6366F1",
              },
            },
          ],
        });
      }

      if (revenueChartRef.current && !revenueChartInstance.current) {
        // Revenue chart
        revenueChartInstance.current = echarts.init(revenueChartRef.current);
        revenueChartInstance.current.setOption({
          animation: false,
          backgroundColor: 'transparent',
          tooltip: {
            trigger: "axis",
            backgroundColor: '#374151',
            borderColor: '#4B5563',
            textStyle: {
              color: '#F3F4F6'
            }
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
            axisLabel: {
              color: '#9CA3AF'
            },
            axisLine: {
              lineStyle: {
                color: '#4B5563'
              }
            }
          },
          yAxis: {
            type: "value",
            axisLabel: {
              color: '#9CA3AF'
            },
            axisLine: {
              lineStyle: {
                color: '#4B5563'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#374151'
              }
            }
          },
          series: [
            {
              name: "Revenue",
              type: "line",
              smooth: true,
              data: [8500, 11000, 12500, 14000, 15500, 17000],
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(99, 102, 241, 0.6)",
                    },
                    {
                      offset: 1,
                      color: "rgba(99, 102, 241, 0.1)",
                    },
                  ],
                },
              },
              lineStyle: {
                color: "#6366F1",
                width: 3,
              },
              itemStyle: {
                color: "#6366F1",
              },
            },
          ],
        });
      }
    };

    // Delay chart initialization to ensure DOM is ready
    const timer = setTimeout(initCharts, 100);

    // Handle resize
    const handleResize = () => {
      userChartInstance.current?.resize();
      revenueChartInstance.current?.resize();
    };

    window.addEventListener("resize", handleResize);

    return () => {
      clearTimeout(timer);
      userChartInstance.current?.dispose();
      revenueChartInstance.current?.dispose();
      window.removeEventListener("resize", handleResize);
      userChartInstance.current = null;
      revenueChartInstance.current = null;
    };
  }, []);

  return (
    <div
      className={`transition-opacity duration-300 ${
        isLoader ? "opacity-0" : "opacity-100"
      }`}
    >
      <LoaderWithText isLoading={isLoader} text="Loading dashboard data..." />
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-5">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Users</p>
              <h3 className="text-2xl font-bold text-white">8,249</h3>
              <p className="text-green-400 text-sm mt-1 flex items-center">
                <ArrowUp className="mr-1 h-4 w-4" />
                <span>12.5% from last month</span>
              </p>
            </div>
            <div className="bg-indigo-900 bg-opacity-50 p-3 rounded-full">
              <Users className="text-indigo-400 w-5 h-5" />
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-5">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Deposits</p>
              <h3 className="text-2xl font-bold text-white">USDT 1.2M</h3>
              <p className="text-green-400 text-sm mt-1 flex items-center">
                <ArrowUp className="mr-1 h-4 w-4" />
                <span>8.2% from last month</span>
              </p>
            </div>
            <div className="bg-green-900 bg-opacity-50 p-3 rounded-full">
              <Banknote className="text-green-400 w-5 h-5" />
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-5">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Daily Profit</p>
              <h3 className="text-2xl font-bold text-white">2.4%</h3>
              <p className="text-red-400 text-sm mt-1 flex items-center">
                <ArrowDown className="mr-1 h-4 w-4" />
                <span>0.5% from yesterday</span>
              </p>
            </div>
            <div className="bg-blue-900 bg-opacity-50 p-3 rounded-full">
              <LineChart className="text-blue-400 w-5 h-5" />
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-5">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Earnings</p>
              <h3 className="text-2xl font-bold text-white">USDT 89,241</h3>
              <p className="text-green-400 text-sm mt-1 flex items-center">
                <ArrowUp className="mr-1 h-4 w-4" />
                <span>15.3% from last month</span>
              </p>
            </div>
            <div className="bg-purple-900 bg-opacity-50 p-3 rounded-full">
              <DollarSign className="text-purple-400 w-5 h-5" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-5">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white">User Growth</h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-xs bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors">
                Monthly
              </button>
              <button className="px-3 py-1 text-xs text-gray-400 hover:bg-gray-700 hover:text-white rounded-full transition-colors">
                Yearly
              </button>
            </div>
          </div>
          <div ref={userChartRef} className="h-64 w-full"></div>
        </div>
        
        <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-5">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white">
              Revenue Overview
            </h3>
            <div className="flex space-x-2">
              <button className="px-3 py-1 text-xs bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors">
                Monthly
              </button>
              <button className="px-3 py-1 text-xs text-gray-400 hover:bg-gray-700 hover:text-white rounded-full transition-colors">
                Yearly
              </button>
            </div>
          </div>
          <div ref={revenueChartRef} className="h-64 w-full"></div>
        </div>
      </div>
      
      {/* Recent Activity */}
      <div className="bg-gray-800 rounded-lg shadow-lg border border-gray-700 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">
            Recent Activity
          </h3>
        </div>
        <div className="divide-y divide-gray-700">
          {[
            {
              icon: "fa-user-plus",
              iconBg: "bg-green-900 bg-opacity-50",
              iconColor: "text-green-400",
              title: "New user registered",
              description: "John Smith completed registration",
              time: "2 minutes ago",
            },
            {
              icon: "fa-money-bill-wave",
              iconBg: "bg-blue-900 bg-opacity-50",
              iconColor: "text-blue-400",
              title: "Deposit request",
              description: "Emma Johnson deposited $5,000",
              time: "15 minutes ago",
            },
            {
              icon: "fa-hand-holding-usd",
              iconBg: "bg-yellow-900 bg-opacity-50",
              iconColor: "text-yellow-400",
              title: "Withdrawal request",
              description: "Michael Brown requested $2,500 withdrawal",
              time: "1 hour ago",
            },
            {
              icon: "fa-check-circle",
              iconBg: "bg-purple-900 bg-opacity-50",
              iconColor: "text-purple-400",
              title: "KYC verified",
              description: "Sarah Davis completed KYC verification",
              time: "3 hours ago",
            },
            {
              icon: "fa-chart-line",
              iconBg: "bg-indigo-900 bg-opacity-50",
              iconColor: "text-indigo-400",
              title: "Profit rate updated",
              description: "Daily profit rate updated to 2.4%",
              time: "5 hours ago",
            },
          ].map((item, index) => {
            const LucideIcon = iconMap[item.icon];

            return (
              <div key={index} className="px-6 py-4 hover:bg-gray-750 transition-colors">
                <div className="flex items-start">
                  <div className={`${item.iconBg} p-2 rounded-full mr-4 flex-shrink-0`}>
                    <LucideIcon className={`${item.iconColor} w-5 h-5`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-white">
                      {item.title}
                    </p>
                    <p className="text-sm text-gray-400 truncate">{item.description}</p>
                  </div>
                  <span className="text-xs text-gray-500 flex-shrink-0 ml-2">{item.time}</span>
                </div>
              </div>
            );
          })}
        </div>
        <div className="px-6 py-3 bg-gray-750 text-center border-t border-gray-700">
          <button className="text-indigo-400 hover:text-indigo-300 text-sm font-medium transition-colors">
            View All Activity
          </button>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboardContent;