import React from 'react'
import AdminSideBar from '../../components/sidebar/AdminSideBar'
import { Outlet } from 'react-router-dom'

function Admin() {
  return (
    <div className="min-h-screen bg-[#0F1115] flex flex-col">
          
          {/* Main Content */}
          <div className="flex flex-1 pt-16">
            <AdminSideBar />
            {/* Mobile Sidebar Toggle
            <div className="fixed bottom-4 right-4 md:hidden z-20">
              <button className="bg-indigo-600 text-white h-12 w-12 rounded-full shadow-lg flex items-center justify-center cursor-pointer">
                <Menu className="w-6 h-6" />
              </button>
            </div> */}
            {/* Main Content Area */}
            <main className="flex-1 ml-0 md:ml-64 min-h-screen">
              <Outlet />
            </main>
          </div>
        </div>
  )
}

export default Admin